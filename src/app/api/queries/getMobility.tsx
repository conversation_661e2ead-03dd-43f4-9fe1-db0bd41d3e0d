import type * as SchemaTypes from '../types';

import type { MobilityData_MobilityAdditionalInfo_Fragment, MobilityData_MobilityAddon_Fragment } from '../fragments/MobilityData';
import type { MobilityAddonDataFragment } from '../fragments/MobilityAddonData';
import type { ModuleWithPermissionsSpecs_AdyenPaymentModule_Fragment, ModuleWithPermissionsSpecs_AppointmentModule_Fragment, ModuleWithPermissionsSpecs_AutoplayModule_Fragment, ModuleWithPermissionsSpecs_BankModule_Fragment, ModuleWithPermissionsSpecs_BasicSigningModule_Fragment, ModuleWithPermissionsSpecs_CapModule_Fragment, ModuleWithPermissionsSpecs_ConfiguratorModule_Fragment, ModuleWithPermissionsSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleWithPermissionsSpecs_CtsModule_Fragment, ModuleWithPermissionsSpecs_DocusignModule_Fragment, ModuleWithPermissionsSpecs_EventApplicationModule_Fragment, ModuleWithPermissionsSpecs_FinderApplicationPrivateModule_Fragment, ModuleWithPermissionsSpecs_FinderApplicationPublicModule_Fragment, ModuleWithPermissionsSpecs_FinderVehicleManagementModule_Fragment, ModuleWithPermissionsSpecs_FiservPaymentModule_Fragment, ModuleWithPermissionsSpecs_GiftVoucherModule_Fragment, ModuleWithPermissionsSpecs_InsuranceModule_Fragment, ModuleWithPermissionsSpecs_LabelsModule_Fragment, ModuleWithPermissionsSpecs_LaunchPadModule_Fragment, ModuleWithPermissionsSpecs_LocalCustomerManagementModule_Fragment, ModuleWithPermissionsSpecs_MaintenanceModule_Fragment, ModuleWithPermissionsSpecs_MarketingModule_Fragment, ModuleWithPermissionsSpecs_MobilityModule_Fragment, ModuleWithPermissionsSpecs_MyInfoModule_Fragment, ModuleWithPermissionsSpecs_NamirialSigningModule_Fragment, ModuleWithPermissionsSpecs_OfrModule_Fragment, ModuleWithPermissionsSpecs_OidcModule_Fragment, ModuleWithPermissionsSpecs_PayGatePaymentModule_Fragment, ModuleWithPermissionsSpecs_PorscheIdModule_Fragment, ModuleWithPermissionsSpecs_PorscheMasterDataModule_Fragment, ModuleWithPermissionsSpecs_PorschePaymentModule_Fragment, ModuleWithPermissionsSpecs_PorscheRetainModule_Fragment, ModuleWithPermissionsSpecs_PromoCodeModule_Fragment, ModuleWithPermissionsSpecs_SalesControlBoardModule_Fragment, ModuleWithPermissionsSpecs_SalesOfferModule_Fragment, ModuleWithPermissionsSpecs_SimpleVehicleManagementModule_Fragment, ModuleWithPermissionsSpecs_StandardApplicationModule_Fragment, ModuleWithPermissionsSpecs_TradeInModule_Fragment, ModuleWithPermissionsSpecs_TtbPaymentModule_Fragment, ModuleWithPermissionsSpecs_UserlikeChatbotModule_Fragment, ModuleWithPermissionsSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleWithPermissionsSpecs_VisitAppointmentModule_Fragment, ModuleWithPermissionsSpecs_WebsiteModule_Fragment, ModuleWithPermissionsSpecs_WhatsappLiveChatModule_Fragment } from '../fragments/ModuleWithPermissionsSpecs';
import type { ConsentsAndDeclarationsModuleWithPermissionsSpecsFragment } from '../fragments/ConsentsAndDeclarationsModuleWithPermissionsSpecs';
import type { ConsentsAndDeclarationsModuleSpecsFragment } from '../fragments/ConsentsAndDeclarationsModuleSpecs';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { SimpleVehicleManagementModuleWithPermissionsSpecsFragment } from '../fragments/SimpleVehicleManagementModuleWithPermissionsSpecs';
import type { SimpleVehicleManagementModuleSpecsFragment } from '../fragments/SimpleVehicleManagementModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleWithPermissionsSpecsFragment } from '../fragments/LocalCustomerManagementModuleWithPermissionsSpecs';
import type { LocalCustomerManagementModuleSpecsFragment } from '../fragments/LocalCustomerManagementModuleSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import type { KycExtraSettingsSpecsFragment } from '../fragments/KYCExtraSettingsSpecs';
import type { KycPresetsSpecFragment } from '../fragments/KYCPresetsSpec';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { BankModuleWithPermissionsSpecsFragment } from '../fragments/BankModuleWithPermissionsSpecs';
import type { BankModuleSpecsFragment } from '../fragments/BankModuleSpecs';
import type { BasicSigningModuleWithPermissionsSpecsFragment } from '../fragments/BasicSigningModuleWithPermissionsSpecs';
import type { BasicSigningModuleSpecsFragment } from '../fragments/BasicSigningModuleSpecs';
import type { NamirialSigningModuleWithPermissionsSpecsFragment } from '../fragments/NamirialSigningModuleWithPermissionsSpecs';
import type { NamirialSigningModuleSpecsFragment } from '../fragments/NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from '../fragments/NamirialSettingsSpec';
import type { StandardApplicationModuleWithPermissionsSpecsFragment } from '../fragments/StandardApplicationModuleWithPermissionsSpecs';
import type { StandardApplicationModuleSpecsFragment } from '../fragments/StandardApplicationModuleSpecs';
import type { DealerPriceDisclaimerDataFragment } from '../fragments/DealerPriceDisclaimerData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { DepositAmountDataFragment } from '../fragments/DepositAmountData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from '../fragments/ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from '../fragments/DealerMarketData';
import type { BankDealerMarketDataFragment } from '../fragments/BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from '../fragments/NzFeesDealerMarketData';
import type { DealerVehiclesSpecsFragment } from '../fragments/DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from '../fragments/DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductListData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OfrModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from '../fragments/BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from '../fragments/BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from '../fragments/DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from '../fragments/InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from '../fragments/ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { KycPresetsOptionsDataFragment } from '../fragments/KYCPresetsOptionsData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from '../fragments/DealershipSettingSpecData';
import type { FlexibleDiscountDataFragment } from '../fragments/FlexibleDiscountData';
import type { CounterSettingsSpecsFragment } from '../fragments/CounterSettingsSpecs';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { DealerUploadedFileWithPreviewDataFragment } from '../fragments/DealerUploadedFileWithPreview';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from '../fragments/DealerBooleanSettingData';
import type { EventApplicationModuleWithPermissionsSpecsFragment } from '../fragments/EventApplicationModuleWithPermissionsSpecs';
import type { EventApplicationModuleSpecsFragment } from '../fragments/EventApplicationModuleSpecs';
import type { AppointmentModuleOnEventModuleDataFragment } from '../fragments/AppointmentModuleOnEventModuleData';
import type { AppointmentTimeSlotDataFragment } from '../fragments/AppointmentTimeSlotData';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from '../fragments/AppointmentModuleEmailContentsSpecs';
import type { EventApplicationModuleEmailContentSpecsFragment, EventEmailContentSpecsFragment } from '../fragments/EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { AdyenPaymentModuleWithPermissionsSpecsFragment } from '../fragments/AdyenPaymentModuleWithPermissionsSpecs';
import type { AdyenPaymentModuleSpecsFragment } from '../fragments/AdyenPaymentModuleSpecs';
import type { AdyenPaymentSettingsSpecFragment } from '../fragments/AdyenPaymentSettingsSpec';
import type { PorschePaymentModuleWithPermissionsSpecsFragment } from '../fragments/PorschePaymentModuleWithPermissionsSpecs';
import type { PorschePaymentModuleSpecsFragment } from '../fragments/PorschePaymentModuleSpecs';
import type { PorschePaymentSettingsSpecFragment } from '../fragments/PorschePaymentSettingsSpec';
import type { FiservPaymentModuleWithPermissionsSpecsFragment } from '../fragments/FiservPaymentModuleWithPermissionsSpecs';
import type { FiservPaymentModuleSpecsFragment } from '../fragments/FiservPaymentModuleSpecs';
import type { FiservPaymentSettingsSpecFragment } from '../fragments/FiservPaymentSettingsSpec';
import type { PayGatePaymentModuleWithPermissionsSpecsFragment } from '../fragments/PayGatePaymentModuleWithPermissionsSpecs';
import type { PayGatePaymentModuleSpecsFragment } from '../fragments/PayGatePaymentModuleSpecs';
import type { PayGatePaymentSettingsSpecFragment } from '../fragments/PayGatePaymentSettingsSpec';
import type { TtbPaymentModuleWithPermissionsSpecsFragment } from '../fragments/TtbPaymentModuleWithPermissionsSpecs';
import type { TtbPaymentModuleSpecsFragment } from '../fragments/TtbPaymentModuleSpecs';
import type { TtbPaymentSettingsSpecFragment } from '../fragments/TtbPaymentSettingsSpec';
import type { MyInfoModuleWithPermissionsSpecsFragment } from '../fragments/MyInfoModuleWithPermissionsSpecs';
import type { MyInfoModuleSpecsFragment } from '../fragments/MyInfoModuleSpecs';
import type { MyInfoSettingSpecFragment } from '../fragments/MyInfoSettingSpec';
import type { ConfiguratorModuleWithPermissionsSpecsFragment } from '../fragments/ConfiguratorModuleWithPermissionsSpecs';
import type { ConfiguratorModuleSpecsFragment } from '../fragments/ConfiguratorModuleSpecs';
import type { ConfiguratorModuleEmailContentSpecsFragment } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import type { WhatsappLiveChatModuleWithPermissionsSpecsFragment } from '../fragments/WhatsappLiveChatModuleWithPermissionsSpecs';
import type { WhatsappLiveChatModuleSpecsFragment } from '../fragments/WhatsappLiveChatModuleSpecs';
import type { WhatsappLiveChatSettingsSpecFragment } from '../fragments/WhatsappLiveChatSettingsSpec';
import type { UserlikeChatbotModuleWithPermissionsSpecsFragment } from '../fragments/UserlikeChatbotModuleWithPermissionsSpecs';
import type { UserlikeChatbotModuleSpecsFragment } from '../fragments/UserlikeChatbotModuleSpecs';
import type { UserlikeChatbotSettingsSpecFragment } from '../fragments/UserlikeChatbotSettingsSpec';
import type { PromoCodeModuleWithPermissionsSpecsFragment } from '../fragments/PromoCodeModuleWithPermissionsSpecs';
import type { PromoCodeModuleSpecsFragment } from '../fragments/PromoCodeModuleSpecs';
import type { MaintenanceModuleWithPermissionsSpecsFragment } from '../fragments/MaintenanceModuleWithPermissionsSpecs';
import type { MaintenanceModuleSpecsFragment } from '../fragments/MaintenanceModuleSpecs';
import type { WebsiteModuleWithPermissionsSpecsFragment } from '../fragments/WebsiteModuleWithPermissionsSpecs';
import type { WebsiteModuleSpecsFragment } from '../fragments/WebsiteModuleSpecs';
import type { EdmSocialMediaDataFragment } from '../fragments/EdmSocialMediaData';
import type { MobilityModuleWithPermissionsSpecsFragment } from '../fragments/MobilityModuleWithPermissionsSpecs';
import type { MobilityModuleSpecsFragment } from '../fragments/MobilityModuleSpecs';
import type { DealerBookingCodeSpecsFragment } from '../fragments/DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from '../fragments/MobilitySigningSettingSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from '../fragments/MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from '../fragments/MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from '../fragments/MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from '../fragments/MobilityHomeDeliveryData';
import type { LabelsModuleWithPermissionsSpecsFragment } from '../fragments/LabelsModuleWithPermissionsSpecs';
import type { LabelsModuleSpecsFragment } from '../fragments/LabelsModuleSpecs';
import type { FinderVehicleManagementModuleWithPermissionsSpecsFragment } from '../fragments/FinderVehicleManagementModuleWithPermissionsSpecs';
import type { FinderVehicleManagementModuleSpecsFragment } from '../fragments/FinderVehicleManagementModuleSpecs';
import type { FinderApplicationPublicModuleWithPermissionsSpecsFragment } from '../fragments/FinderApplicationPublicModuleWithPermissionsSpecs';
import type { FinderApplicationPublicModuleSpecsFragment } from '../fragments/FinderApplicationPublicModuleSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import type { ModuleDisclaimersDataFragment } from '../fragments/ModuleDisclaimersData';
import type { FinderApplicationPrivateModuleWithPermissionsSpecsFragment } from '../fragments/FinderApplicationPrivateModuleWithPermissionsSpecs';
import type { FinderApplicationPrivateModuleSpecsFragment } from '../fragments/FinderApplicationPrivateModuleSpecs';
import type { AutoplayModuleWithPermissionsSpecsFragment } from '../fragments/AutoplayModuleWithPermissionsSpecs';
import type { AutoplayModuleSpecsFragment } from '../fragments/AutoplayModuleSpecs';
import type { AutoplaySettingSpecsFragment } from '../fragments/AutoplaySettingSpecs';
import type { CtsModuleWithPermissionsSpecsFragment } from '../fragments/CtsModuleWithPermissionsSpecs';
import type { CtsModuleSpecsFragment } from '../fragments/CtsModuleSpecs';
import type { CtsModuleSettingDataFragment } from '../fragments/CtsModuleSettingData';
import type { AppointmentModuleWithPermissionsSpecsFragment } from '../fragments/AppointmentModuleWithPermissionsSpecs';
import type { AppointmentModuleSpecsFragment } from '../fragments/AppointmentModuleSpecs';
import type { InsuranceModuleWithPermissionsSpecsFragment } from '../fragments/InsuranceModuleWithPermissionsSpecs';
import type { InsuranceModuleSpecsFragment } from '../fragments/InsuranceModuleSpecs';
import type { PorscheMasterDataModuleWithPermissionsSpecsFragment } from '../fragments/PorscheMasterDataModuleWithPermissionsSpecs';
import type { PorscheMasterDataModuleSpecsFragment } from '../fragments/PorscheMasterDataModuleSpecs';
import type { GiftVoucherModuleWithPermissionsSpecsFragment } from '../fragments/GiftVoucherModuleWithPermissionsSpecs';
import type { GiftVoucherModuleSpecsFragment } from '../fragments/GiftVoucherModuleSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import type { TradeInModuleWithPermissionsSpecsFragment } from '../fragments/TradeInModuleWithPermissionsSpecs';
import type { TradeInModuleSpecsFragment } from '../fragments/TradeInModuleSpecs';
import type { TradeInSettingSpecFragment } from '../fragments/TradeInSetting';
import type { CapModuleWithPermissionsSpecsFragment } from '../fragments/CapModuleWithPermissionsSpecs';
import type { CapModuleSpecsFragment } from '../fragments/CapModuleSpecs';
import type { CapSettingSpecFragment } from '../fragments/CapSettingSpec';
import type { PorscheIdModuleWithPermissionsSpecsFragment } from '../fragments/PorscheIdModuleWithPermissionsSpecs';
import type { PorscheIdModuleSpecsFragment } from '../fragments/PorscheIdModuleSpecs';
import type { PorscheIdSettingSpecFragment } from '../fragments/PorscheIdSettingSpec';
import type { PorscheRetainModuleWithPermissionsSpecsFragment } from '../fragments/PorscheRetainModuleWithPermissionsSpecs';
import type { PorscheRetainModuleSpecsFragment } from '../fragments/PorscheRetainModuleSpecs';
import type { DocusignModuleSpecsWithPermissionSpecsFragment } from '../fragments/DocusignModuleSpecsWithPermissionSpecs';
import type { DocusignModuleSpecsFragment } from '../fragments/DocusignModuleSpecs';
import type { DocusignSettingDataFragment } from '../fragments/DocusignSettingData';
import type { LaunchPadModuleWithPermissionsSpecsFragment } from '../fragments/LaunchPadModuleWithPermissionsSpecs';
import type { LaunchPadModuleSpecsFragment } from '../fragments/LaunchPadModuleSpecs';
import type { VisitAppointmentModuleSpecsFragment } from '../fragments/VisitAppointmentModuleSpecs';
import type { TimeSlotDataFragment } from '../fragments/TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import type { VisitAppointmentModuleWithPermissionsSpecsFragment } from '../fragments/VisitAppointmentModuleWithPermissionsSpecs';
import type { OidcModuleSpecsFragment } from '../fragments/OIDCModuleSpecs';
import type { MarketingModuleWithPermissionsSpecsFragment } from '../fragments/MarketingModuleWithPermissionsSpecs';
import type { MarketingModuleSpecsFragment } from '../fragments/MarketingModuleSpecs';
import type { SalesOfferModuleWithPermissionsSpecsFragment } from '../fragments/SalesOfferModuleWithPermissionsSpecs';
import type { SalesOfferModuleSpecsFragment } from '../fragments/SalesOfferModuleSpecs';
import type { BankDetailsDataFragment } from '../fragments/BankDetailsData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from '../fragments/BankIntegrationData';
import type { UploadFileFormDataFragment } from '../fragments/UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductDetailsData';
import type { PaymentSettingsDetailsFragment } from '../fragments/PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from '../fragments/LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from '../fragments/TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from '../fragments/InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from '../fragments/DownPaymentSettingsDetails';
import type { LeaseSettingsDetailsFragment } from '../fragments/LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from '../fragments/DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from '../fragments/ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from '../fragments/LocalUcclLeasingOnlyDetails';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import type { VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecs';
import type { SalesControlBoardModuleWithPermissionsSpecsFragment } from '../fragments/SalesControlBoardModuleWithPermissionsSpecs';
import type { SalesControlBoardModuleSpecsFragment } from '../fragments/SalesControlBoardModuleSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from '../fragments/DealerIntData';
import type { OfrModuleSpecsFragment } from '../fragments/OFRModuleSpecs';
import type { OfrModuleEmailContentsSpecsFragment, OfrSalesConsultantEmailContentSpecsFragment, OfrSalesConsultantEmailContentContextSpecsFragment, OfrCustomerEmailContentSpecsFragment, OfrCustomerEmailContentContextSpecsFragment } from '../fragments/OFRModuleEmailContentsSpecs';
import type { OfrEquityDataFragment } from '../fragments/OFREquityData';
import type { MobilityAdditionalInfoDataFragment } from '../fragments/MobilityAdditionalInfoData';
import { gql } from '@apollo/client';
import { MobilityDataFragmentDoc } from '../fragments/MobilityData';
import { MobilityAddonDataFragmentDoc } from '../fragments/MobilityAddonData';
import { ModuleWithPermissionsSpecsFragmentDoc } from '../fragments/ModuleWithPermissionsSpecs';
import { ConsentsAndDeclarationsModuleWithPermissionsSpecsFragmentDoc } from '../fragments/ConsentsAndDeclarationsModuleWithPermissionsSpecs';
import { ConsentsAndDeclarationsModuleSpecsFragmentDoc } from '../fragments/ConsentsAndDeclarationsModuleSpecs';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { SimpleVehicleManagementModuleWithPermissionsSpecsFragmentDoc } from '../fragments/SimpleVehicleManagementModuleWithPermissionsSpecs';
import { SimpleVehicleManagementModuleSpecsFragmentDoc } from '../fragments/SimpleVehicleManagementModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleWithPermissionsSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleWithPermissionsSpecs';
import { LocalCustomerManagementModuleSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import { KycExtraSettingsSpecsFragmentDoc } from '../fragments/KYCExtraSettingsSpecs';
import { KycPresetsSpecFragmentDoc } from '../fragments/KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { BankModuleWithPermissionsSpecsFragmentDoc } from '../fragments/BankModuleWithPermissionsSpecs';
import { BankModuleSpecsFragmentDoc } from '../fragments/BankModuleSpecs';
import { BasicSigningModuleWithPermissionsSpecsFragmentDoc } from '../fragments/BasicSigningModuleWithPermissionsSpecs';
import { BasicSigningModuleSpecsFragmentDoc } from '../fragments/BasicSigningModuleSpecs';
import { NamirialSigningModuleWithPermissionsSpecsFragmentDoc } from '../fragments/NamirialSigningModuleWithPermissionsSpecs';
import { NamirialSigningModuleSpecsFragmentDoc } from '../fragments/NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from '../fragments/NamirialSettingsSpec';
import { StandardApplicationModuleWithPermissionsSpecsFragmentDoc } from '../fragments/StandardApplicationModuleWithPermissionsSpecs';
import { StandardApplicationModuleSpecsFragmentDoc } from '../fragments/StandardApplicationModuleSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from '../fragments/DealerPriceDisclaimerData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { DepositAmountDataFragmentDoc } from '../fragments/DepositAmountData';
import { ApplicationMarketTypeFragmentFragmentDoc } from '../fragments/ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from '../fragments/DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from '../fragments/BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from '../fragments/NzFeesDealerMarketData';
import { DealerVehiclesSpecsFragmentDoc } from '../fragments/DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from '../fragments/DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from '../fragments/FinanceProductListData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from '../fragments/BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from '../fragments/BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from '../fragments/DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from '../fragments/InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from '../fragments/ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { KycPresetsOptionsDataFragmentDoc } from '../fragments/KYCPresetsOptionsData';
import { DealershipSettingSpecDataFragmentDoc } from '../fragments/DealershipSettingSpecData';
import { FlexibleDiscountDataFragmentDoc } from '../fragments/FlexibleDiscountData';
import { CounterSettingsSpecsFragmentDoc } from '../fragments/CounterSettingsSpecs';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from '../fragments/DealerUploadedFileWithPreview';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from '../fragments/DealerBooleanSettingData';
import { EventApplicationModuleWithPermissionsSpecsFragmentDoc } from '../fragments/EventApplicationModuleWithPermissionsSpecs';
import { EventApplicationModuleSpecsFragmentDoc } from '../fragments/EventApplicationModuleSpecs';
import { AppointmentModuleOnEventModuleDataFragmentDoc } from '../fragments/AppointmentModuleOnEventModuleData';
import { AppointmentTimeSlotDataFragmentDoc } from '../fragments/AppointmentTimeSlotData';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from '../fragments/AppointmentModuleEmailContentsSpecs';
import { EventApplicationModuleEmailContentSpecsFragmentDoc, EventEmailContentSpecsFragmentDoc } from '../fragments/EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { AdyenPaymentModuleWithPermissionsSpecsFragmentDoc } from '../fragments/AdyenPaymentModuleWithPermissionsSpecs';
import { AdyenPaymentModuleSpecsFragmentDoc } from '../fragments/AdyenPaymentModuleSpecs';
import { AdyenPaymentSettingsSpecFragmentDoc } from '../fragments/AdyenPaymentSettingsSpec';
import { PorschePaymentModuleWithPermissionsSpecsFragmentDoc } from '../fragments/PorschePaymentModuleWithPermissionsSpecs';
import { PorschePaymentModuleSpecsFragmentDoc } from '../fragments/PorschePaymentModuleSpecs';
import { PorschePaymentSettingsSpecFragmentDoc } from '../fragments/PorschePaymentSettingsSpec';
import { FiservPaymentModuleWithPermissionsSpecsFragmentDoc } from '../fragments/FiservPaymentModuleWithPermissionsSpecs';
import { FiservPaymentModuleSpecsFragmentDoc } from '../fragments/FiservPaymentModuleSpecs';
import { FiservPaymentSettingsSpecFragmentDoc } from '../fragments/FiservPaymentSettingsSpec';
import { PayGatePaymentModuleWithPermissionsSpecsFragmentDoc } from '../fragments/PayGatePaymentModuleWithPermissionsSpecs';
import { PayGatePaymentModuleSpecsFragmentDoc } from '../fragments/PayGatePaymentModuleSpecs';
import { PayGatePaymentSettingsSpecFragmentDoc } from '../fragments/PayGatePaymentSettingsSpec';
import { TtbPaymentModuleWithPermissionsSpecsFragmentDoc } from '../fragments/TtbPaymentModuleWithPermissionsSpecs';
import { TtbPaymentModuleSpecsFragmentDoc } from '../fragments/TtbPaymentModuleSpecs';
import { TtbPaymentSettingsSpecFragmentDoc } from '../fragments/TtbPaymentSettingsSpec';
import { MyInfoModuleWithPermissionsSpecsFragmentDoc } from '../fragments/MyInfoModuleWithPermissionsSpecs';
import { MyInfoModuleSpecsFragmentDoc } from '../fragments/MyInfoModuleSpecs';
import { MyInfoSettingSpecFragmentDoc } from '../fragments/MyInfoSettingSpec';
import { ConfiguratorModuleWithPermissionsSpecsFragmentDoc } from '../fragments/ConfiguratorModuleWithPermissionsSpecs';
import { ConfiguratorModuleSpecsFragmentDoc } from '../fragments/ConfiguratorModuleSpecs';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import { WhatsappLiveChatModuleWithPermissionsSpecsFragmentDoc } from '../fragments/WhatsappLiveChatModuleWithPermissionsSpecs';
import { WhatsappLiveChatModuleSpecsFragmentDoc } from '../fragments/WhatsappLiveChatModuleSpecs';
import { WhatsappLiveChatSettingsSpecFragmentDoc } from '../fragments/WhatsappLiveChatSettingsSpec';
import { UserlikeChatbotModuleWithPermissionsSpecsFragmentDoc } from '../fragments/UserlikeChatbotModuleWithPermissionsSpecs';
import { UserlikeChatbotModuleSpecsFragmentDoc } from '../fragments/UserlikeChatbotModuleSpecs';
import { UserlikeChatbotSettingsSpecFragmentDoc } from '../fragments/UserlikeChatbotSettingsSpec';
import { PromoCodeModuleWithPermissionsSpecsFragmentDoc } from '../fragments/PromoCodeModuleWithPermissionsSpecs';
import { PromoCodeModuleSpecsFragmentDoc } from '../fragments/PromoCodeModuleSpecs';
import { MaintenanceModuleWithPermissionsSpecsFragmentDoc } from '../fragments/MaintenanceModuleWithPermissionsSpecs';
import { MaintenanceModuleSpecsFragmentDoc } from '../fragments/MaintenanceModuleSpecs';
import { WebsiteModuleWithPermissionsSpecsFragmentDoc } from '../fragments/WebsiteModuleWithPermissionsSpecs';
import { WebsiteModuleSpecsFragmentDoc } from '../fragments/WebsiteModuleSpecs';
import { EdmSocialMediaDataFragmentDoc } from '../fragments/EdmSocialMediaData';
import { MobilityModuleWithPermissionsSpecsFragmentDoc } from '../fragments/MobilityModuleWithPermissionsSpecs';
import { MobilityModuleSpecsFragmentDoc } from '../fragments/MobilityModuleSpecs';
import { DealerBookingCodeSpecsFragmentDoc } from '../fragments/DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from '../fragments/MobilitySigningSettingSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from '../fragments/MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from '../fragments/MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from '../fragments/MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from '../fragments/MobilityHomeDeliveryData';
import { LabelsModuleWithPermissionsSpecsFragmentDoc } from '../fragments/LabelsModuleWithPermissionsSpecs';
import { LabelsModuleSpecsFragmentDoc } from '../fragments/LabelsModuleSpecs';
import { FinderVehicleManagementModuleWithPermissionsSpecsFragmentDoc } from '../fragments/FinderVehicleManagementModuleWithPermissionsSpecs';
import { FinderVehicleManagementModuleSpecsFragmentDoc } from '../fragments/FinderVehicleManagementModuleSpecs';
import { FinderApplicationPublicModuleWithPermissionsSpecsFragmentDoc } from '../fragments/FinderApplicationPublicModuleWithPermissionsSpecs';
import { FinderApplicationPublicModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPublicModuleSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import { ModuleDisclaimersDataFragmentDoc } from '../fragments/ModuleDisclaimersData';
import { FinderApplicationPrivateModuleWithPermissionsSpecsFragmentDoc } from '../fragments/FinderApplicationPrivateModuleWithPermissionsSpecs';
import { FinderApplicationPrivateModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPrivateModuleSpecs';
import { AutoplayModuleWithPermissionsSpecsFragmentDoc } from '../fragments/AutoplayModuleWithPermissionsSpecs';
import { AutoplayModuleSpecsFragmentDoc } from '../fragments/AutoplayModuleSpecs';
import { AutoplaySettingSpecsFragmentDoc } from '../fragments/AutoplaySettingSpecs';
import { CtsModuleWithPermissionsSpecsFragmentDoc } from '../fragments/CtsModuleWithPermissionsSpecs';
import { CtsModuleSpecsFragmentDoc } from '../fragments/CtsModuleSpecs';
import { CtsModuleSettingDataFragmentDoc } from '../fragments/CtsModuleSettingData';
import { AppointmentModuleWithPermissionsSpecsFragmentDoc } from '../fragments/AppointmentModuleWithPermissionsSpecs';
import { AppointmentModuleSpecsFragmentDoc } from '../fragments/AppointmentModuleSpecs';
import { InsuranceModuleWithPermissionsSpecsFragmentDoc } from '../fragments/InsuranceModuleWithPermissionsSpecs';
import { InsuranceModuleSpecsFragmentDoc } from '../fragments/InsuranceModuleSpecs';
import { PorscheMasterDataModuleWithPermissionsSpecsFragmentDoc } from '../fragments/PorscheMasterDataModuleWithPermissionsSpecs';
import { PorscheMasterDataModuleSpecsFragmentDoc } from '../fragments/PorscheMasterDataModuleSpecs';
import { GiftVoucherModuleWithPermissionsSpecsFragmentDoc } from '../fragments/GiftVoucherModuleWithPermissionsSpecs';
import { GiftVoucherModuleSpecsFragmentDoc } from '../fragments/GiftVoucherModuleSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import { TradeInModuleWithPermissionsSpecsFragmentDoc } from '../fragments/TradeInModuleWithPermissionsSpecs';
import { TradeInModuleSpecsFragmentDoc } from '../fragments/TradeInModuleSpecs';
import { TradeInSettingSpecFragmentDoc } from '../fragments/TradeInSetting';
import { CapModuleWithPermissionsSpecsFragmentDoc } from '../fragments/CapModuleWithPermissionsSpecs';
import { CapModuleSpecsFragmentDoc } from '../fragments/CapModuleSpecs';
import { CapSettingSpecFragmentDoc } from '../fragments/CapSettingSpec';
import { PorscheIdModuleWithPermissionsSpecsFragmentDoc } from '../fragments/PorscheIdModuleWithPermissionsSpecs';
import { PorscheIdModuleSpecsFragmentDoc } from '../fragments/PorscheIdModuleSpecs';
import { PorscheIdSettingSpecFragmentDoc } from '../fragments/PorscheIdSettingSpec';
import { PorscheRetainModuleWithPermissionsSpecsFragmentDoc } from '../fragments/PorscheRetainModuleWithPermissionsSpecs';
import { PorscheRetainModuleSpecsFragmentDoc } from '../fragments/PorscheRetainModuleSpecs';
import { DocusignModuleSpecsWithPermissionSpecsFragmentDoc } from '../fragments/DocusignModuleSpecsWithPermissionSpecs';
import { DocusignModuleSpecsFragmentDoc } from '../fragments/DocusignModuleSpecs';
import { DocusignSettingDataFragmentDoc } from '../fragments/DocusignSettingData';
import { LaunchPadModuleWithPermissionsSpecsFragmentDoc } from '../fragments/LaunchPadModuleWithPermissionsSpecs';
import { LaunchPadModuleSpecsFragmentDoc } from '../fragments/LaunchPadModuleSpecs';
import { VisitAppointmentModuleSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleSpecs';
import { TimeSlotDataFragmentDoc } from '../fragments/TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import { VisitAppointmentModuleWithPermissionsSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleWithPermissionsSpecs';
import { OidcModuleSpecsFragmentDoc } from '../fragments/OIDCModuleSpecs';
import { MarketingModuleWithPermissionsSpecsFragmentDoc } from '../fragments/MarketingModuleWithPermissionsSpecs';
import { MarketingModuleSpecsFragmentDoc } from '../fragments/MarketingModuleSpecs';
import { SalesOfferModuleWithPermissionsSpecsFragmentDoc } from '../fragments/SalesOfferModuleWithPermissionsSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from '../fragments/SalesOfferModuleSpecs';
import { BankDetailsDataFragmentDoc } from '../fragments/BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from '../fragments/BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from '../fragments/UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from '../fragments/FinanceProductDetailsData';
import { PaymentSettingsDetailsFragmentDoc } from '../fragments/PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from '../fragments/LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from '../fragments/TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from '../fragments/InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from '../fragments/DownPaymentSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from '../fragments/LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from '../fragments/DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from '../fragments/ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from '../fragments/LocalUcclLeasingOnlyDetails';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import { VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecs';
import { SalesControlBoardModuleWithPermissionsSpecsFragmentDoc } from '../fragments/SalesControlBoardModuleWithPermissionsSpecs';
import { SalesControlBoardModuleSpecsFragmentDoc } from '../fragments/SalesControlBoardModuleSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from '../fragments/DealerIntData';
import { OfrModuleSpecsFragmentDoc } from '../fragments/OFRModuleSpecs';
import { OfrModuleEmailContentsSpecsFragmentDoc, OfrSalesConsultantEmailContentSpecsFragmentDoc, OfrSalesConsultantEmailContentContextSpecsFragmentDoc, OfrCustomerEmailContentSpecsFragmentDoc, OfrCustomerEmailContentContextSpecsFragmentDoc } from '../fragments/OFRModuleEmailContentsSpecs';
import { OfrEquityDataFragmentDoc } from '../fragments/OFREquityData';
import { MobilityAdditionalInfoDataFragmentDoc } from '../fragments/MobilityAdditionalInfoData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetMobilityQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetMobilityQuery = (
  { __typename: 'Query' }
  & { event: (
    { __typename: 'MobilityAdditionalInfo' }
    & MobilityData_MobilityAdditionalInfo_Fragment
  ) | (
    { __typename: 'MobilityAddon' }
    & MobilityData_MobilityAddon_Fragment
  ) }
);


export const GetMobilityDocument = /*#__PURE__*/ gql`
    query getMobility($id: ObjectID!) {
  event: getMobility(id: $id) {
    ...MobilityData
  }
}
    ${MobilityDataFragmentDoc}
${MobilityAddonDataFragmentDoc}
${ModuleWithPermissionsSpecsFragmentDoc}
${ConsentsAndDeclarationsModuleWithPermissionsSpecsFragmentDoc}
${ConsentsAndDeclarationsModuleSpecsFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${SimpleVehicleManagementModuleWithPermissionsSpecsFragmentDoc}
${SimpleVehicleManagementModuleSpecsFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${LocalCustomerManagementModuleWithPermissionsSpecsFragmentDoc}
${LocalCustomerManagementModuleSpecsFragmentDoc}
${LocalCustomerManagementModuleKycFieldSpecsFragmentDoc}
${KycExtraSettingsSpecsFragmentDoc}
${KycPresetsSpecFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${BankModuleWithPermissionsSpecsFragmentDoc}
${BankModuleSpecsFragmentDoc}
${BasicSigningModuleWithPermissionsSpecsFragmentDoc}
${BasicSigningModuleSpecsFragmentDoc}
${NamirialSigningModuleWithPermissionsSpecsFragmentDoc}
${NamirialSigningModuleSpecsFragmentDoc}
${NamirialSettingsSpecFragmentDoc}
${StandardApplicationModuleWithPermissionsSpecsFragmentDoc}
${StandardApplicationModuleSpecsFragmentDoc}
${DealerPriceDisclaimerDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${DepositAmountDataFragmentDoc}
${ApplicationMarketTypeFragmentFragmentDoc}
${DealerMarketDataFragmentDoc}
${BankDealerMarketDataFragmentDoc}
${NzFeesDealerMarketDataFragmentDoc}
${DealerVehiclesSpecsFragmentDoc}
${DealerFinanceProductsSpecsFragmentDoc}
${FinanceProductListDataFragmentDoc}
${PeriodDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${VehicleReferenceParametersDataFragmentDoc}
${BalloonSettingsDetailsFragmentDoc}
${BalloonGfvSettingsDetailsFragmentDoc}
${DealerInsuranceProductsSpecsFragmentDoc}
${InsuranceProductListDataFragmentDoc}
${ErgoLookupTableSettingsDetailsFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${DealerDisclaimersConfiguratorDataFragmentDoc}
${KycPresetsOptionsDataFragmentDoc}
${DealershipSettingSpecDataFragmentDoc}
${FlexibleDiscountDataFragmentDoc}
${CounterSettingsSpecsFragmentDoc}
${StandardApplicationModuleEmailContentsSpecsFragmentDoc}
${StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc}
${StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${DealerUploadedFileWithPreviewDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${DealerBooleanSettingDataFragmentDoc}
${StandardApplicationModuleEmailContentSpecsFragmentDoc}
${StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc}
${EventApplicationModuleWithPermissionsSpecsFragmentDoc}
${EventApplicationModuleSpecsFragmentDoc}
${AppointmentModuleOnEventModuleDataFragmentDoc}
${AppointmentTimeSlotDataFragmentDoc}
${AppointmentModuleEmailContentsSpecsFragmentDoc}
${AppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${AppointmentModuleEmailContentSpecsFragmentDoc}
${AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc}
${EventApplicationModuleEmailContentSpecsFragmentDoc}
${TranslatedTextDataFragmentDoc}
${AdyenPaymentModuleWithPermissionsSpecsFragmentDoc}
${AdyenPaymentModuleSpecsFragmentDoc}
${AdyenPaymentSettingsSpecFragmentDoc}
${PorschePaymentModuleWithPermissionsSpecsFragmentDoc}
${PorschePaymentModuleSpecsFragmentDoc}
${PorschePaymentSettingsSpecFragmentDoc}
${FiservPaymentModuleWithPermissionsSpecsFragmentDoc}
${FiservPaymentModuleSpecsFragmentDoc}
${FiservPaymentSettingsSpecFragmentDoc}
${PayGatePaymentModuleWithPermissionsSpecsFragmentDoc}
${PayGatePaymentModuleSpecsFragmentDoc}
${PayGatePaymentSettingsSpecFragmentDoc}
${TtbPaymentModuleWithPermissionsSpecsFragmentDoc}
${TtbPaymentModuleSpecsFragmentDoc}
${TtbPaymentSettingsSpecFragmentDoc}
${MyInfoModuleWithPermissionsSpecsFragmentDoc}
${MyInfoModuleSpecsFragmentDoc}
${MyInfoSettingSpecFragmentDoc}
${ConfiguratorModuleWithPermissionsSpecsFragmentDoc}
${ConfiguratorModuleSpecsFragmentDoc}
${ConfiguratorModuleEmailContentSpecsFragmentDoc}
${WhatsappLiveChatModuleWithPermissionsSpecsFragmentDoc}
${WhatsappLiveChatModuleSpecsFragmentDoc}
${WhatsappLiveChatSettingsSpecFragmentDoc}
${UserlikeChatbotModuleWithPermissionsSpecsFragmentDoc}
${UserlikeChatbotModuleSpecsFragmentDoc}
${UserlikeChatbotSettingsSpecFragmentDoc}
${PromoCodeModuleWithPermissionsSpecsFragmentDoc}
${PromoCodeModuleSpecsFragmentDoc}
${MaintenanceModuleWithPermissionsSpecsFragmentDoc}
${MaintenanceModuleSpecsFragmentDoc}
${WebsiteModuleWithPermissionsSpecsFragmentDoc}
${WebsiteModuleSpecsFragmentDoc}
${EdmSocialMediaDataFragmentDoc}
${MobilityModuleWithPermissionsSpecsFragmentDoc}
${MobilityModuleSpecsFragmentDoc}
${DealerBookingCodeSpecsFragmentDoc}
${MobilitySigningSettingSpecsFragmentDoc}
${MobilityModuleEmailScenarioContentSpecsFragmentDoc}
${MobilityCustomerEmailContentDataFragmentDoc}
${MobilityEmailContentDataFragmentDoc}
${MobilityOperatorEmailContentDataFragmentDoc}
${MobilityHomeDeliveryDataFragmentDoc}
${LabelsModuleWithPermissionsSpecsFragmentDoc}
${LabelsModuleSpecsFragmentDoc}
${FinderVehicleManagementModuleWithPermissionsSpecsFragmentDoc}
${FinderVehicleManagementModuleSpecsFragmentDoc}
${FinderApplicationPublicModuleWithPermissionsSpecsFragmentDoc}
${FinderApplicationPublicModuleSpecsFragmentDoc}
${FinderApplicationModuleEmailContentSpecsFragmentDoc}
${ModuleDisclaimersDataFragmentDoc}
${FinderApplicationPrivateModuleWithPermissionsSpecsFragmentDoc}
${FinderApplicationPrivateModuleSpecsFragmentDoc}
${AutoplayModuleWithPermissionsSpecsFragmentDoc}
${AutoplayModuleSpecsFragmentDoc}
${AutoplaySettingSpecsFragmentDoc}
${CtsModuleWithPermissionsSpecsFragmentDoc}
${CtsModuleSpecsFragmentDoc}
${CtsModuleSettingDataFragmentDoc}
${AppointmentModuleWithPermissionsSpecsFragmentDoc}
${AppointmentModuleSpecsFragmentDoc}
${InsuranceModuleWithPermissionsSpecsFragmentDoc}
${InsuranceModuleSpecsFragmentDoc}
${PorscheMasterDataModuleWithPermissionsSpecsFragmentDoc}
${PorscheMasterDataModuleSpecsFragmentDoc}
${GiftVoucherModuleWithPermissionsSpecsFragmentDoc}
${GiftVoucherModuleSpecsFragmentDoc}
${GiftVoucherModuleEmailContentsSpecsFragmentDoc}
${GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc}
${GiftVoucherModuleEmailDataFragmentDoc}
${TradeInModuleWithPermissionsSpecsFragmentDoc}
${TradeInModuleSpecsFragmentDoc}
${TradeInSettingSpecFragmentDoc}
${CapModuleWithPermissionsSpecsFragmentDoc}
${CapModuleSpecsFragmentDoc}
${CapSettingSpecFragmentDoc}
${PorscheIdModuleWithPermissionsSpecsFragmentDoc}
${PorscheIdModuleSpecsFragmentDoc}
${PorscheIdSettingSpecFragmentDoc}
${PorscheRetainModuleWithPermissionsSpecsFragmentDoc}
${PorscheRetainModuleSpecsFragmentDoc}
${DocusignModuleSpecsWithPermissionSpecsFragmentDoc}
${DocusignModuleSpecsFragmentDoc}
${DocusignSettingDataFragmentDoc}
${LaunchPadModuleWithPermissionsSpecsFragmentDoc}
${LaunchPadModuleSpecsFragmentDoc}
${VisitAppointmentModuleSpecsFragmentDoc}
${TimeSlotDataFragmentDoc}
${VisitAppointmentModuleEmailContentsSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${VisitAppointmentModuleWithPermissionsSpecsFragmentDoc}
${OidcModuleSpecsFragmentDoc}
${MarketingModuleWithPermissionsSpecsFragmentDoc}
${MarketingModuleSpecsFragmentDoc}
${SalesOfferModuleWithPermissionsSpecsFragmentDoc}
${SalesOfferModuleSpecsFragmentDoc}
${BankDetailsDataFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${BankIntegrationDataFragmentDoc}
${UploadFileFormDataFragmentDoc}
${FinanceProductDetailsDataFragmentDoc}
${PaymentSettingsDetailsFragmentDoc}
${LoanSettingsDetailsFragmentDoc}
${TermSettingsDetailsFragmentDoc}
${InterestRateSettingsDetailsFragmentDoc}
${DownPaymentSettingsDetailsFragmentDoc}
${LeaseSettingsDetailsFragmentDoc}
${DepositSettingsDetailsFragmentDoc}
${ResidualValueSettingsDetailsFragmentDoc}
${LocalUcclLeasingOnlyDetailsFragmentDoc}
${SalesOfferModuleEmailContentsSpecsFragmentDoc}
${SalesOfferEmailContentsSpecsFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragmentDoc}
${SalesControlBoardModuleWithPermissionsSpecsFragmentDoc}
${SalesControlBoardModuleSpecsFragmentDoc}
${DealerIntDataFragmentDoc}
${DealerFloatDataFragmentDoc}
${DealerObjectIdDataFragmentDoc}
${OfrModuleSpecsFragmentDoc}
${OfrModuleEmailContentsSpecsFragmentDoc}
${OfrSalesConsultantEmailContentSpecsFragmentDoc}
${OfrSalesConsultantEmailContentContextSpecsFragmentDoc}
${OfrCustomerEmailContentSpecsFragmentDoc}
${OfrCustomerEmailContentContextSpecsFragmentDoc}
${OfrEquityDataFragmentDoc}
${MobilityAdditionalInfoDataFragmentDoc}`;

/**
 * __useGetMobilityQuery__
 *
 * To run a query within a React component, call `useGetMobilityQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMobilityQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMobilityQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetMobilityQuery(baseOptions: Apollo.QueryHookOptions<GetMobilityQuery, GetMobilityQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMobilityQuery, GetMobilityQueryVariables>(GetMobilityDocument, options);
      }
export function useGetMobilityLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMobilityQuery, GetMobilityQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMobilityQuery, GetMobilityQueryVariables>(GetMobilityDocument, options);
        }
export type GetMobilityQueryHookResult = ReturnType<typeof useGetMobilityQuery>;
export type GetMobilityLazyQueryHookResult = ReturnType<typeof useGetMobilityLazyQuery>;
export type GetMobilityQueryResult = Apollo.QueryResult<GetMobilityQuery, GetMobilityQueryVariables>;