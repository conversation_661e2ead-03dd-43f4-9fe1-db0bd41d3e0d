import type * as SchemaTypes from '../types';

import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { EventApplicationModuleEmailContentSpecsFragment, EventEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import { gql } from '@apollo/client';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { EventApplicationModuleEmailContentSpecsFragmentDoc, EventEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
export type EventApplicationModuleInDealerSpecsFragment = (
  { __typename: 'EventApplicationModule' }
  & Pick<SchemaTypes.EventApplicationModule, 'id' | 'vehicleModuleId' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ), dealerVehicles: Array<(
    { __typename: 'DealerVehicles' }
    & DealerVehiclesSpecsFragment
  )>, emailContents: (
    { __typename: 'EventApplicationModuleEmailContents' }
    & EventApplicationModuleEmailContentSpecsFragment
  ) }
);

export const EventApplicationModuleInDealerSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment EventApplicationModuleInDealerSpecs on EventApplicationModule {
  id
  company {
    id
    currency
    displayName
    timeZone
    countryCode
  }
  vehicleModuleId
  displayName
  dealerVehicles {
    ...DealerVehiclesSpecs
  }
  emailContents {
    ...EventApplicationModuleEmailContentSpecs
  }
}
    `;