import type * as SchemaTypes from '../types';

import type { ConsentsAndDeclarationsModuleWithPermissionsSpecsFragment } from './ConsentsAndDeclarationsModuleWithPermissionsSpecs';
import type { ConsentsAndDeclarationsModuleSpecsFragment } from './ConsentsAndDeclarationsModuleSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { SimpleVehicleManagementModuleWithPermissionsSpecsFragment } from './SimpleVehicleManagementModuleWithPermissionsSpecs';
import type { SimpleVehicleManagementModuleSpecsFragment } from './SimpleVehicleManagementModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleWithPermissionsSpecsFragment } from './LocalCustomerManagementModuleWithPermissionsSpecs';
import type { LocalCustomerManagementModuleSpecsFragment } from './LocalCustomerManagementModuleSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from './LocalCustomerManagementModuleKycFieldSpecs';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { KycPresetsSpecFragment } from './KYCPresetsSpec';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { BankModuleWithPermissionsSpecsFragment } from './BankModuleWithPermissionsSpecs';
import type { BankModuleSpecsFragment } from './BankModuleSpecs';
import type { BasicSigningModuleWithPermissionsSpecsFragment } from './BasicSigningModuleWithPermissionsSpecs';
import type { BasicSigningModuleSpecsFragment } from './BasicSigningModuleSpecs';
import type { NamirialSigningModuleWithPermissionsSpecsFragment } from './NamirialSigningModuleWithPermissionsSpecs';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { StandardApplicationModuleWithPermissionsSpecsFragment } from './StandardApplicationModuleWithPermissionsSpecs';
import type { StandardApplicationModuleSpecsFragment } from './StandardApplicationModuleSpecs';
import type { DealerPriceDisclaimerDataFragment } from './DealerPriceDisclaimerData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { DepositAmountDataFragment } from './DepositAmountData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { PeriodDataFragment } from './PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OfrModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { KycPresetsOptionsDataFragment } from './KYCPresetsOptionsData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { FlexibleDiscountDataFragment } from './FlexibleDiscountData';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from './StandardApplicationModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerUploadedFileWithPreviewDataFragment } from './DealerUploadedFileWithPreview';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { EventApplicationModuleWithPermissionsSpecsFragment } from './EventApplicationModuleWithPermissionsSpecs';
import type { EventApplicationModuleSpecsFragment } from './EventApplicationModuleSpecs';
import type { AppointmentModuleOnEventModuleDataFragment } from './AppointmentModuleOnEventModuleData';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { EventApplicationModuleEmailContentSpecsFragment, EventEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { AdyenPaymentModuleWithPermissionsSpecsFragment } from './AdyenPaymentModuleWithPermissionsSpecs';
import type { AdyenPaymentModuleSpecsFragment } from './AdyenPaymentModuleSpecs';
import type { AdyenPaymentSettingsSpecFragment } from './AdyenPaymentSettingsSpec';
import type { PorschePaymentModuleWithPermissionsSpecsFragment } from './PorschePaymentModuleWithPermissionsSpecs';
import type { PorschePaymentModuleSpecsFragment } from './PorschePaymentModuleSpecs';
import type { PorschePaymentSettingsSpecFragment } from './PorschePaymentSettingsSpec';
import type { FiservPaymentModuleWithPermissionsSpecsFragment } from './FiservPaymentModuleWithPermissionsSpecs';
import type { FiservPaymentModuleSpecsFragment } from './FiservPaymentModuleSpecs';
import type { FiservPaymentSettingsSpecFragment } from './FiservPaymentSettingsSpec';
import type { PayGatePaymentModuleWithPermissionsSpecsFragment } from './PayGatePaymentModuleWithPermissionsSpecs';
import type { PayGatePaymentModuleSpecsFragment } from './PayGatePaymentModuleSpecs';
import type { PayGatePaymentSettingsSpecFragment } from './PayGatePaymentSettingsSpec';
import type { TtbPaymentModuleWithPermissionsSpecsFragment } from './TtbPaymentModuleWithPermissionsSpecs';
import type { TtbPaymentModuleSpecsFragment } from './TtbPaymentModuleSpecs';
import type { TtbPaymentSettingsSpecFragment } from './TtbPaymentSettingsSpec';
import type { MyInfoModuleWithPermissionsSpecsFragment } from './MyInfoModuleWithPermissionsSpecs';
import type { MyInfoModuleSpecsFragment } from './MyInfoModuleSpecs';
import type { MyInfoSettingSpecFragment } from './MyInfoSettingSpec';
import type { ConfiguratorModuleWithPermissionsSpecsFragment } from './ConfiguratorModuleWithPermissionsSpecs';
import type { ConfiguratorModuleSpecsFragment } from './ConfiguratorModuleSpecs';
import type { ConfiguratorModuleEmailContentSpecsFragment } from './ConfiguratorModuleEmailContentSpecs';
import type { WhatsappLiveChatModuleWithPermissionsSpecsFragment } from './WhatsappLiveChatModuleWithPermissionsSpecs';
import type { WhatsappLiveChatModuleSpecsFragment } from './WhatsappLiveChatModuleSpecs';
import type { WhatsappLiveChatSettingsSpecFragment } from './WhatsappLiveChatSettingsSpec';
import type { UserlikeChatbotModuleWithPermissionsSpecsFragment } from './UserlikeChatbotModuleWithPermissionsSpecs';
import type { UserlikeChatbotModuleSpecsFragment } from './UserlikeChatbotModuleSpecs';
import type { UserlikeChatbotSettingsSpecFragment } from './UserlikeChatbotSettingsSpec';
import type { PromoCodeModuleWithPermissionsSpecsFragment } from './PromoCodeModuleWithPermissionsSpecs';
import type { PromoCodeModuleSpecsFragment } from './PromoCodeModuleSpecs';
import type { MaintenanceModuleWithPermissionsSpecsFragment } from './MaintenanceModuleWithPermissionsSpecs';
import type { MaintenanceModuleSpecsFragment } from './MaintenanceModuleSpecs';
import type { WebsiteModuleWithPermissionsSpecsFragment } from './WebsiteModuleWithPermissionsSpecs';
import type { WebsiteModuleSpecsFragment } from './WebsiteModuleSpecs';
import type { EdmSocialMediaDataFragment } from './EdmSocialMediaData';
import type { MobilityModuleWithPermissionsSpecsFragment } from './MobilityModuleWithPermissionsSpecs';
import type { MobilityModuleSpecsFragment } from './MobilityModuleSpecs';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from './MobilitySigningSettingSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { LabelsModuleWithPermissionsSpecsFragment } from './LabelsModuleWithPermissionsSpecs';
import type { LabelsModuleSpecsFragment } from './LabelsModuleSpecs';
import type { FinderVehicleManagementModuleWithPermissionsSpecsFragment } from './FinderVehicleManagementModuleWithPermissionsSpecs';
import type { FinderVehicleManagementModuleSpecsFragment } from './FinderVehicleManagementModuleSpecs';
import type { FinderApplicationPublicModuleWithPermissionsSpecsFragment } from './FinderApplicationPublicModuleWithPermissionsSpecs';
import type { FinderApplicationPublicModuleSpecsFragment } from './FinderApplicationPublicModuleSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from './FinderApplicationModuleEmailContentSpecs';
import type { ModuleDisclaimersDataFragment } from './ModuleDisclaimersData';
import type { FinderApplicationPrivateModuleWithPermissionsSpecsFragment } from './FinderApplicationPrivateModuleWithPermissionsSpecs';
import type { FinderApplicationPrivateModuleSpecsFragment } from './FinderApplicationPrivateModuleSpecs';
import type { AutoplayModuleWithPermissionsSpecsFragment } from './AutoplayModuleWithPermissionsSpecs';
import type { AutoplayModuleSpecsFragment } from './AutoplayModuleSpecs';
import type { AutoplaySettingSpecsFragment } from './AutoplaySettingSpecs';
import type { CtsModuleWithPermissionsSpecsFragment } from './CtsModuleWithPermissionsSpecs';
import type { CtsModuleSpecsFragment } from './CtsModuleSpecs';
import type { CtsModuleSettingDataFragment } from './CtsModuleSettingData';
import type { AppointmentModuleWithPermissionsSpecsFragment } from './AppointmentModuleWithPermissionsSpecs';
import type { AppointmentModuleSpecsFragment } from './AppointmentModuleSpecs';
import type { InsuranceModuleWithPermissionsSpecsFragment } from './InsuranceModuleWithPermissionsSpecs';
import type { InsuranceModuleSpecsFragment } from './InsuranceModuleSpecs';
import type { PorscheMasterDataModuleWithPermissionsSpecsFragment } from './PorscheMasterDataModuleWithPermissionsSpecs';
import type { PorscheMasterDataModuleSpecsFragment } from './PorscheMasterDataModuleSpecs';
import type { GiftVoucherModuleWithPermissionsSpecsFragment } from './GiftVoucherModuleWithPermissionsSpecs';
import type { GiftVoucherModuleSpecsFragment } from './GiftVoucherModuleSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from './GiftVoucherModuleEmailContentsSpecs';
import type { TradeInModuleWithPermissionsSpecsFragment } from './TradeInModuleWithPermissionsSpecs';
import type { TradeInModuleSpecsFragment } from './TradeInModuleSpecs';
import type { TradeInSettingSpecFragment } from './TradeInSetting';
import type { CapModuleWithPermissionsSpecsFragment } from './CapModuleWithPermissionsSpecs';
import type { CapModuleSpecsFragment } from './CapModuleSpecs';
import type { CapSettingSpecFragment } from './CapSettingSpec';
import type { PorscheIdModuleWithPermissionsSpecsFragment } from './PorscheIdModuleWithPermissionsSpecs';
import type { PorscheIdModuleSpecsFragment } from './PorscheIdModuleSpecs';
import type { PorscheIdSettingSpecFragment } from './PorscheIdSettingSpec';
import type { PorscheRetainModuleWithPermissionsSpecsFragment } from './PorscheRetainModuleWithPermissionsSpecs';
import type { PorscheRetainModuleSpecsFragment } from './PorscheRetainModuleSpecs';
import type { DocusignModuleSpecsWithPermissionSpecsFragment } from './DocusignModuleSpecsWithPermissionSpecs';
import type { DocusignModuleSpecsFragment } from './DocusignModuleSpecs';
import type { DocusignSettingDataFragment } from './DocusignSettingData';
import type { LaunchPadModuleWithPermissionsSpecsFragment } from './LaunchPadModuleWithPermissionsSpecs';
import type { LaunchPadModuleSpecsFragment } from './LaunchPadModuleSpecs';
import type { VisitAppointmentModuleSpecsFragment } from './VisitAppointmentModuleSpecs';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { VisitAppointmentModuleWithPermissionsSpecsFragment } from './VisitAppointmentModuleWithPermissionsSpecs';
import type { OidcModuleSpecsFragment } from './OIDCModuleSpecs';
import type { MarketingModuleWithPermissionsSpecsFragment } from './MarketingModuleWithPermissionsSpecs';
import type { MarketingModuleSpecsFragment } from './MarketingModuleSpecs';
import type { SalesOfferModuleWithPermissionsSpecsFragment } from './SalesOfferModuleWithPermissionsSpecs';
import type { SalesOfferModuleSpecsFragment } from './SalesOfferModuleSpecs';
import type { BankDetailsDataFragment } from './BankDetailsData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecs';
import type { SalesControlBoardModuleWithPermissionsSpecsFragment } from './SalesControlBoardModuleWithPermissionsSpecs';
import type { SalesControlBoardModuleSpecsFragment } from './SalesControlBoardModuleSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from './DealerIntData';
import type { OfrModuleSpecsFragment } from './OFRModuleSpecs';
import type { OfrModuleEmailContentsSpecsFragment, OfrSalesConsultantEmailContentSpecsFragment, OfrSalesConsultantEmailContentContextSpecsFragment, OfrCustomerEmailContentSpecsFragment, OfrCustomerEmailContentContextSpecsFragment } from './OFRModuleEmailContentsSpecs';
import type { OfrEquityDataFragment } from './OFREquityData';
import { gql } from '@apollo/client';
import { ConsentsAndDeclarationsModuleWithPermissionsSpecsFragmentDoc } from './ConsentsAndDeclarationsModuleWithPermissionsSpecs';
import { ConsentsAndDeclarationsModuleSpecsFragmentDoc } from './ConsentsAndDeclarationsModuleSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { SimpleVehicleManagementModuleWithPermissionsSpecsFragmentDoc } from './SimpleVehicleManagementModuleWithPermissionsSpecs';
import { SimpleVehicleManagementModuleSpecsFragmentDoc } from './SimpleVehicleManagementModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleWithPermissionsSpecsFragmentDoc } from './LocalCustomerManagementModuleWithPermissionsSpecs';
import { LocalCustomerManagementModuleSpecsFragmentDoc } from './LocalCustomerManagementModuleSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from './LocalCustomerManagementModuleKycFieldSpecs';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { KycPresetsSpecFragmentDoc } from './KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { BankModuleWithPermissionsSpecsFragmentDoc } from './BankModuleWithPermissionsSpecs';
import { BankModuleSpecsFragmentDoc } from './BankModuleSpecs';
import { BasicSigningModuleWithPermissionsSpecsFragmentDoc } from './BasicSigningModuleWithPermissionsSpecs';
import { BasicSigningModuleSpecsFragmentDoc } from './BasicSigningModuleSpecs';
import { NamirialSigningModuleWithPermissionsSpecsFragmentDoc } from './NamirialSigningModuleWithPermissionsSpecs';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { StandardApplicationModuleWithPermissionsSpecsFragmentDoc } from './StandardApplicationModuleWithPermissionsSpecs';
import { StandardApplicationModuleSpecsFragmentDoc } from './StandardApplicationModuleSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from './DealerPriceDisclaimerData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { DepositAmountDataFragmentDoc } from './DepositAmountData';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { KycPresetsOptionsDataFragmentDoc } from './KYCPresetsOptionsData';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { FlexibleDiscountDataFragmentDoc } from './FlexibleDiscountData';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from './StandardApplicationModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from './DealerUploadedFileWithPreview';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { EventApplicationModuleWithPermissionsSpecsFragmentDoc } from './EventApplicationModuleWithPermissionsSpecs';
import { EventApplicationModuleSpecsFragmentDoc } from './EventApplicationModuleSpecs';
import { AppointmentModuleOnEventModuleDataFragmentDoc } from './AppointmentModuleOnEventModuleData';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { EventApplicationModuleEmailContentSpecsFragmentDoc, EventEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { AdyenPaymentModuleWithPermissionsSpecsFragmentDoc } from './AdyenPaymentModuleWithPermissionsSpecs';
import { AdyenPaymentModuleSpecsFragmentDoc } from './AdyenPaymentModuleSpecs';
import { AdyenPaymentSettingsSpecFragmentDoc } from './AdyenPaymentSettingsSpec';
import { PorschePaymentModuleWithPermissionsSpecsFragmentDoc } from './PorschePaymentModuleWithPermissionsSpecs';
import { PorschePaymentModuleSpecsFragmentDoc } from './PorschePaymentModuleSpecs';
import { PorschePaymentSettingsSpecFragmentDoc } from './PorschePaymentSettingsSpec';
import { FiservPaymentModuleWithPermissionsSpecsFragmentDoc } from './FiservPaymentModuleWithPermissionsSpecs';
import { FiservPaymentModuleSpecsFragmentDoc } from './FiservPaymentModuleSpecs';
import { FiservPaymentSettingsSpecFragmentDoc } from './FiservPaymentSettingsSpec';
import { PayGatePaymentModuleWithPermissionsSpecsFragmentDoc } from './PayGatePaymentModuleWithPermissionsSpecs';
import { PayGatePaymentModuleSpecsFragmentDoc } from './PayGatePaymentModuleSpecs';
import { PayGatePaymentSettingsSpecFragmentDoc } from './PayGatePaymentSettingsSpec';
import { TtbPaymentModuleWithPermissionsSpecsFragmentDoc } from './TtbPaymentModuleWithPermissionsSpecs';
import { TtbPaymentModuleSpecsFragmentDoc } from './TtbPaymentModuleSpecs';
import { TtbPaymentSettingsSpecFragmentDoc } from './TtbPaymentSettingsSpec';
import { MyInfoModuleWithPermissionsSpecsFragmentDoc } from './MyInfoModuleWithPermissionsSpecs';
import { MyInfoModuleSpecsFragmentDoc } from './MyInfoModuleSpecs';
import { MyInfoSettingSpecFragmentDoc } from './MyInfoSettingSpec';
import { ConfiguratorModuleWithPermissionsSpecsFragmentDoc } from './ConfiguratorModuleWithPermissionsSpecs';
import { ConfiguratorModuleSpecsFragmentDoc } from './ConfiguratorModuleSpecs';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from './ConfiguratorModuleEmailContentSpecs';
import { WhatsappLiveChatModuleWithPermissionsSpecsFragmentDoc } from './WhatsappLiveChatModuleWithPermissionsSpecs';
import { WhatsappLiveChatModuleSpecsFragmentDoc } from './WhatsappLiveChatModuleSpecs';
import { WhatsappLiveChatSettingsSpecFragmentDoc } from './WhatsappLiveChatSettingsSpec';
import { UserlikeChatbotModuleWithPermissionsSpecsFragmentDoc } from './UserlikeChatbotModuleWithPermissionsSpecs';
import { UserlikeChatbotModuleSpecsFragmentDoc } from './UserlikeChatbotModuleSpecs';
import { UserlikeChatbotSettingsSpecFragmentDoc } from './UserlikeChatbotSettingsSpec';
import { PromoCodeModuleWithPermissionsSpecsFragmentDoc } from './PromoCodeModuleWithPermissionsSpecs';
import { PromoCodeModuleSpecsFragmentDoc } from './PromoCodeModuleSpecs';
import { MaintenanceModuleWithPermissionsSpecsFragmentDoc } from './MaintenanceModuleWithPermissionsSpecs';
import { MaintenanceModuleSpecsFragmentDoc } from './MaintenanceModuleSpecs';
import { WebsiteModuleWithPermissionsSpecsFragmentDoc } from './WebsiteModuleWithPermissionsSpecs';
import { WebsiteModuleSpecsFragmentDoc } from './WebsiteModuleSpecs';
import { EdmSocialMediaDataFragmentDoc } from './EdmSocialMediaData';
import { MobilityModuleWithPermissionsSpecsFragmentDoc } from './MobilityModuleWithPermissionsSpecs';
import { MobilityModuleSpecsFragmentDoc } from './MobilityModuleSpecs';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from './MobilitySigningSettingSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { LabelsModuleWithPermissionsSpecsFragmentDoc } from './LabelsModuleWithPermissionsSpecs';
import { LabelsModuleSpecsFragmentDoc } from './LabelsModuleSpecs';
import { FinderVehicleManagementModuleWithPermissionsSpecsFragmentDoc } from './FinderVehicleManagementModuleWithPermissionsSpecs';
import { FinderVehicleManagementModuleSpecsFragmentDoc } from './FinderVehicleManagementModuleSpecs';
import { FinderApplicationPublicModuleWithPermissionsSpecsFragmentDoc } from './FinderApplicationPublicModuleWithPermissionsSpecs';
import { FinderApplicationPublicModuleSpecsFragmentDoc } from './FinderApplicationPublicModuleSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from './FinderApplicationModuleEmailContentSpecs';
import { ModuleDisclaimersDataFragmentDoc } from './ModuleDisclaimersData';
import { FinderApplicationPrivateModuleWithPermissionsSpecsFragmentDoc } from './FinderApplicationPrivateModuleWithPermissionsSpecs';
import { FinderApplicationPrivateModuleSpecsFragmentDoc } from './FinderApplicationPrivateModuleSpecs';
import { AutoplayModuleWithPermissionsSpecsFragmentDoc } from './AutoplayModuleWithPermissionsSpecs';
import { AutoplayModuleSpecsFragmentDoc } from './AutoplayModuleSpecs';
import { AutoplaySettingSpecsFragmentDoc } from './AutoplaySettingSpecs';
import { CtsModuleWithPermissionsSpecsFragmentDoc } from './CtsModuleWithPermissionsSpecs';
import { CtsModuleSpecsFragmentDoc } from './CtsModuleSpecs';
import { CtsModuleSettingDataFragmentDoc } from './CtsModuleSettingData';
import { AppointmentModuleWithPermissionsSpecsFragmentDoc } from './AppointmentModuleWithPermissionsSpecs';
import { AppointmentModuleSpecsFragmentDoc } from './AppointmentModuleSpecs';
import { InsuranceModuleWithPermissionsSpecsFragmentDoc } from './InsuranceModuleWithPermissionsSpecs';
import { InsuranceModuleSpecsFragmentDoc } from './InsuranceModuleSpecs';
import { PorscheMasterDataModuleWithPermissionsSpecsFragmentDoc } from './PorscheMasterDataModuleWithPermissionsSpecs';
import { PorscheMasterDataModuleSpecsFragmentDoc } from './PorscheMasterDataModuleSpecs';
import { GiftVoucherModuleWithPermissionsSpecsFragmentDoc } from './GiftVoucherModuleWithPermissionsSpecs';
import { GiftVoucherModuleSpecsFragmentDoc } from './GiftVoucherModuleSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from './GiftVoucherModuleEmailContentsSpecs';
import { TradeInModuleWithPermissionsSpecsFragmentDoc } from './TradeInModuleWithPermissionsSpecs';
import { TradeInModuleSpecsFragmentDoc } from './TradeInModuleSpecs';
import { TradeInSettingSpecFragmentDoc } from './TradeInSetting';
import { CapModuleWithPermissionsSpecsFragmentDoc } from './CapModuleWithPermissionsSpecs';
import { CapModuleSpecsFragmentDoc } from './CapModuleSpecs';
import { CapSettingSpecFragmentDoc } from './CapSettingSpec';
import { PorscheIdModuleWithPermissionsSpecsFragmentDoc } from './PorscheIdModuleWithPermissionsSpecs';
import { PorscheIdModuleSpecsFragmentDoc } from './PorscheIdModuleSpecs';
import { PorscheIdSettingSpecFragmentDoc } from './PorscheIdSettingSpec';
import { PorscheRetainModuleWithPermissionsSpecsFragmentDoc } from './PorscheRetainModuleWithPermissionsSpecs';
import { PorscheRetainModuleSpecsFragmentDoc } from './PorscheRetainModuleSpecs';
import { DocusignModuleSpecsWithPermissionSpecsFragmentDoc } from './DocusignModuleSpecsWithPermissionSpecs';
import { DocusignModuleSpecsFragmentDoc } from './DocusignModuleSpecs';
import { DocusignSettingDataFragmentDoc } from './DocusignSettingData';
import { LaunchPadModuleWithPermissionsSpecsFragmentDoc } from './LaunchPadModuleWithPermissionsSpecs';
import { LaunchPadModuleSpecsFragmentDoc } from './LaunchPadModuleSpecs';
import { VisitAppointmentModuleSpecsFragmentDoc } from './VisitAppointmentModuleSpecs';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { VisitAppointmentModuleWithPermissionsSpecsFragmentDoc } from './VisitAppointmentModuleWithPermissionsSpecs';
import { OidcModuleSpecsFragmentDoc } from './OIDCModuleSpecs';
import { MarketingModuleWithPermissionsSpecsFragmentDoc } from './MarketingModuleWithPermissionsSpecs';
import { MarketingModuleSpecsFragmentDoc } from './MarketingModuleSpecs';
import { SalesOfferModuleWithPermissionsSpecsFragmentDoc } from './SalesOfferModuleWithPermissionsSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from './SalesOfferModuleSpecs';
import { BankDetailsDataFragmentDoc } from './BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecs';
import { SalesControlBoardModuleWithPermissionsSpecsFragmentDoc } from './SalesControlBoardModuleWithPermissionsSpecs';
import { SalesControlBoardModuleSpecsFragmentDoc } from './SalesControlBoardModuleSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from './DealerIntData';
import { OfrModuleSpecsFragmentDoc } from './OFRModuleSpecs';
import { OfrModuleEmailContentsSpecsFragmentDoc, OfrSalesConsultantEmailContentSpecsFragmentDoc, OfrSalesConsultantEmailContentContextSpecsFragmentDoc, OfrCustomerEmailContentSpecsFragmentDoc, OfrCustomerEmailContentContextSpecsFragmentDoc } from './OFRModuleEmailContentsSpecs';
import { OfrEquityDataFragmentDoc } from './OFREquityData';
export type ModuleWithPermissionsSpecs_AdyenPaymentModule_Fragment = (
  { __typename: 'AdyenPaymentModule' }
  & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & AdyenPaymentModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_AppointmentModule_Fragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & AppointmentModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_AutoplayModule_Fragment = (
  { __typename: 'AutoplayModule' }
  & Pick<SchemaTypes.AutoplayModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & AutoplayModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_BankModule_Fragment = (
  { __typename: 'BankModule' }
  & Pick<SchemaTypes.BankModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & BankModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_BasicSigningModule_Fragment = (
  { __typename: 'BasicSigningModule' }
  & Pick<SchemaTypes.BasicSigningModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & BasicSigningModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_CapModule_Fragment = (
  { __typename: 'CapModule' }
  & Pick<SchemaTypes.CapModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & CapModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_ConfiguratorModule_Fragment = (
  { __typename: 'ConfiguratorModule' }
  & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & ConfiguratorModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_ConsentsAndDeclarationsModule_Fragment = (
  { __typename: 'ConsentsAndDeclarationsModule' }
  & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & ConsentsAndDeclarationsModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_CtsModule_Fragment = (
  { __typename: 'CtsModule' }
  & Pick<SchemaTypes.CtsModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & CtsModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_DocusignModule_Fragment = (
  { __typename: 'DocusignModule' }
  & Pick<SchemaTypes.DocusignModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & DocusignModuleSpecsWithPermissionSpecsFragment
);

export type ModuleWithPermissionsSpecs_EventApplicationModule_Fragment = (
  { __typename: 'EventApplicationModule' }
  & Pick<SchemaTypes.EventApplicationModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & EventApplicationModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_FinderApplicationPrivateModule_Fragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & FinderApplicationPrivateModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_FinderApplicationPublicModule_Fragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & FinderApplicationPublicModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_FinderVehicleManagementModule_Fragment = (
  { __typename: 'FinderVehicleManagementModule' }
  & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & FinderVehicleManagementModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_FiservPaymentModule_Fragment = (
  { __typename: 'FiservPaymentModule' }
  & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & FiservPaymentModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_GiftVoucherModule_Fragment = (
  { __typename: 'GiftVoucherModule' }
  & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & GiftVoucherModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_InsuranceModule_Fragment = (
  { __typename: 'InsuranceModule' }
  & Pick<SchemaTypes.InsuranceModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & InsuranceModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_LabelsModule_Fragment = (
  { __typename: 'LabelsModule' }
  & Pick<SchemaTypes.LabelsModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & LabelsModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_LaunchPadModule_Fragment = (
  { __typename: 'LaunchPadModule' }
  & Pick<SchemaTypes.LaunchPadModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & LaunchPadModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_LocalCustomerManagementModule_Fragment = (
  { __typename: 'LocalCustomerManagementModule' }
  & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & LocalCustomerManagementModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_MaintenanceModule_Fragment = (
  { __typename: 'MaintenanceModule' }
  & Pick<SchemaTypes.MaintenanceModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & MaintenanceModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_MarketingModule_Fragment = (
  { __typename: 'MarketingModule' }
  & Pick<SchemaTypes.MarketingModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & MarketingModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_MobilityModule_Fragment = (
  { __typename: 'MobilityModule' }
  & Pick<SchemaTypes.MobilityModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & MobilityModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_MyInfoModule_Fragment = (
  { __typename: 'MyInfoModule' }
  & Pick<SchemaTypes.MyInfoModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & MyInfoModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_NamirialSigningModule_Fragment = (
  { __typename: 'NamirialSigningModule' }
  & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & NamirialSigningModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_OfrModule_Fragment = (
  { __typename: 'OFRModule' }
  & Pick<SchemaTypes.OfrModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & OfrModuleSpecsFragment
);

export type ModuleWithPermissionsSpecs_OidcModule_Fragment = (
  { __typename: 'OIDCModule' }
  & Pick<SchemaTypes.OidcModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & OidcModuleSpecsFragment
);

export type ModuleWithPermissionsSpecs_PayGatePaymentModule_Fragment = (
  { __typename: 'PayGatePaymentModule' }
  & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PayGatePaymentModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_PorscheIdModule_Fragment = (
  { __typename: 'PorscheIdModule' }
  & Pick<SchemaTypes.PorscheIdModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PorscheIdModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_PorscheMasterDataModule_Fragment = (
  { __typename: 'PorscheMasterDataModule' }
  & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PorscheMasterDataModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_PorschePaymentModule_Fragment = (
  { __typename: 'PorschePaymentModule' }
  & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PorschePaymentModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_PorscheRetainModule_Fragment = (
  { __typename: 'PorscheRetainModule' }
  & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PorscheRetainModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_PromoCodeModule_Fragment = (
  { __typename: 'PromoCodeModule' }
  & Pick<SchemaTypes.PromoCodeModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PromoCodeModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_SalesControlBoardModule_Fragment = (
  { __typename: 'SalesControlBoardModule' }
  & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & SalesControlBoardModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_SalesOfferModule_Fragment = (
  { __typename: 'SalesOfferModule' }
  & Pick<SchemaTypes.SalesOfferModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & SalesOfferModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_SimpleVehicleManagementModule_Fragment = (
  { __typename: 'SimpleVehicleManagementModule' }
  & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & SimpleVehicleManagementModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_StandardApplicationModule_Fragment = (
  { __typename: 'StandardApplicationModule' }
  & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & StandardApplicationModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_TradeInModule_Fragment = (
  { __typename: 'TradeInModule' }
  & Pick<SchemaTypes.TradeInModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & TradeInModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_TtbPaymentModule_Fragment = (
  { __typename: 'TtbPaymentModule' }
  & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & TtbPaymentModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_UserlikeChatbotModule_Fragment = (
  { __typename: 'UserlikeChatbotModule' }
  & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & UserlikeChatbotModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment = (
  { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
  & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_VisitAppointmentModule_Fragment = (
  { __typename: 'VisitAppointmentModule' }
  & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & VisitAppointmentModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_WebsiteModule_Fragment = (
  { __typename: 'WebsiteModule' }
  & Pick<SchemaTypes.WebsiteModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & WebsiteModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecs_WhatsappLiveChatModule_Fragment = (
  { __typename: 'WhatsappLiveChatModule' }
  & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & WhatsappLiveChatModuleWithPermissionsSpecsFragment
);

export type ModuleWithPermissionsSpecsFragment = ModuleWithPermissionsSpecs_AdyenPaymentModule_Fragment | ModuleWithPermissionsSpecs_AppointmentModule_Fragment | ModuleWithPermissionsSpecs_AutoplayModule_Fragment | ModuleWithPermissionsSpecs_BankModule_Fragment | ModuleWithPermissionsSpecs_BasicSigningModule_Fragment | ModuleWithPermissionsSpecs_CapModule_Fragment | ModuleWithPermissionsSpecs_ConfiguratorModule_Fragment | ModuleWithPermissionsSpecs_ConsentsAndDeclarationsModule_Fragment | ModuleWithPermissionsSpecs_CtsModule_Fragment | ModuleWithPermissionsSpecs_DocusignModule_Fragment | ModuleWithPermissionsSpecs_EventApplicationModule_Fragment | ModuleWithPermissionsSpecs_FinderApplicationPrivateModule_Fragment | ModuleWithPermissionsSpecs_FinderApplicationPublicModule_Fragment | ModuleWithPermissionsSpecs_FinderVehicleManagementModule_Fragment | ModuleWithPermissionsSpecs_FiservPaymentModule_Fragment | ModuleWithPermissionsSpecs_GiftVoucherModule_Fragment | ModuleWithPermissionsSpecs_InsuranceModule_Fragment | ModuleWithPermissionsSpecs_LabelsModule_Fragment | ModuleWithPermissionsSpecs_LaunchPadModule_Fragment | ModuleWithPermissionsSpecs_LocalCustomerManagementModule_Fragment | ModuleWithPermissionsSpecs_MaintenanceModule_Fragment | ModuleWithPermissionsSpecs_MarketingModule_Fragment | ModuleWithPermissionsSpecs_MobilityModule_Fragment | ModuleWithPermissionsSpecs_MyInfoModule_Fragment | ModuleWithPermissionsSpecs_NamirialSigningModule_Fragment | ModuleWithPermissionsSpecs_OfrModule_Fragment | ModuleWithPermissionsSpecs_OidcModule_Fragment | ModuleWithPermissionsSpecs_PayGatePaymentModule_Fragment | ModuleWithPermissionsSpecs_PorscheIdModule_Fragment | ModuleWithPermissionsSpecs_PorscheMasterDataModule_Fragment | ModuleWithPermissionsSpecs_PorschePaymentModule_Fragment | ModuleWithPermissionsSpecs_PorscheRetainModule_Fragment | ModuleWithPermissionsSpecs_PromoCodeModule_Fragment | ModuleWithPermissionsSpecs_SalesControlBoardModule_Fragment | ModuleWithPermissionsSpecs_SalesOfferModule_Fragment | ModuleWithPermissionsSpecs_SimpleVehicleManagementModule_Fragment | ModuleWithPermissionsSpecs_StandardApplicationModule_Fragment | ModuleWithPermissionsSpecs_TradeInModule_Fragment | ModuleWithPermissionsSpecs_TtbPaymentModule_Fragment | ModuleWithPermissionsSpecs_UserlikeChatbotModule_Fragment | ModuleWithPermissionsSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment | ModuleWithPermissionsSpecs_VisitAppointmentModule_Fragment | ModuleWithPermissionsSpecs_WebsiteModule_Fragment | ModuleWithPermissionsSpecs_WhatsappLiveChatModule_Fragment;

export const ModuleWithPermissionsSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment ModuleWithPermissionsSpecs on Module {
  __typename
  id
  companyId
  company {
    id
    currency
    displayName
    timeZone
    countryCode
  }
  ... on ConsentsAndDeclarationsModule {
    ...ConsentsAndDeclarationsModuleWithPermissionsSpecs
  }
  ... on SimpleVehicleManagementModule {
    ...SimpleVehicleManagementModuleWithPermissionsSpecs
  }
  ... on LocalCustomerManagementModule {
    ...LocalCustomerManagementModuleWithPermissionsSpecs
  }
  ... on BankModule {
    ...BankModuleWithPermissionsSpecs
  }
  ... on BasicSigningModule {
    ...BasicSigningModuleWithPermissionsSpecs
  }
  ... on NamirialSigningModule {
    ...NamirialSigningModuleWithPermissionsSpecs
  }
  ... on StandardApplicationModule {
    ...StandardApplicationModuleWithPermissionsSpecs
  }
  ... on EventApplicationModule {
    ...EventApplicationModuleWithPermissionsSpecs
  }
  ... on AdyenPaymentModule {
    ...AdyenPaymentModuleWithPermissionsSpecs
  }
  ... on PorschePaymentModule {
    ...PorschePaymentModuleWithPermissionsSpecs
  }
  ... on FiservPaymentModule {
    ...FiservPaymentModuleWithPermissionsSpecs
  }
  ... on PayGatePaymentModule {
    ...PayGatePaymentModuleWithPermissionsSpecs
  }
  ... on TtbPaymentModule {
    ...TtbPaymentModuleWithPermissionsSpecs
  }
  ... on MyInfoModule {
    ...MyInfoModuleWithPermissionsSpecs
  }
  ... on ConfiguratorModule {
    ...ConfiguratorModuleWithPermissionsSpecs
  }
  ... on WhatsappLiveChatModule {
    ...WhatsappLiveChatModuleWithPermissionsSpecs
  }
  ... on UserlikeChatbotModule {
    ...UserlikeChatbotModuleWithPermissionsSpecs
  }
  ... on PromoCodeModule {
    ...PromoCodeModuleWithPermissionsSpecs
  }
  ... on MaintenanceModule {
    ...MaintenanceModuleWithPermissionsSpecs
  }
  ... on WebsiteModule {
    ...WebsiteModuleWithPermissionsSpecs
  }
  ... on MobilityModule {
    ...MobilityModuleWithPermissionsSpecs
  }
  ... on LabelsModule {
    ...LabelsModuleWithPermissionsSpecs
  }
  ... on FinderVehicleManagementModule {
    ...FinderVehicleManagementModuleWithPermissionsSpecs
  }
  ... on FinderApplicationPublicModule {
    ...FinderApplicationPublicModuleWithPermissionsSpecs
  }
  ... on FinderApplicationPrivateModule {
    ...FinderApplicationPrivateModuleWithPermissionsSpecs
  }
  ... on AutoplayModule {
    ...AutoplayModuleWithPermissionsSpecs
  }
  ... on CtsModule {
    ...CtsModuleWithPermissionsSpecs
  }
  ... on AppointmentModule {
    ...AppointmentModuleWithPermissionsSpecs
  }
  ... on InsuranceModule {
    ...InsuranceModuleWithPermissionsSpecs
  }
  ... on PorscheMasterDataModule {
    ...PorscheMasterDataModuleWithPermissionsSpecs
  }
  ... on GiftVoucherModule {
    ...GiftVoucherModuleWithPermissionsSpecs
  }
  ... on TradeInModule {
    ...TradeInModuleWithPermissionsSpecs
  }
  ... on CapModule {
    ...CapModuleWithPermissionsSpecs
  }
  ... on PorscheIdModule {
    ...PorscheIdModuleWithPermissionsSpecs
  }
  ... on PorscheRetainModule {
    ...PorscheRetainModuleWithPermissionsSpecs
  }
  ... on DocusignModule {
    ...DocusignModuleSpecsWithPermissionSpecs
  }
  ... on LaunchPadModule {
    ...LaunchPadModuleWithPermissionsSpecs
  }
  ... on VisitAppointmentModule {
    ...VisitAppointmentModuleWithPermissionsSpecs
  }
  ... on OIDCModule {
    ...OIDCModuleSpecs
  }
  ... on MarketingModule {
    ...MarketingModuleWithPermissionsSpecs
  }
  ... on SalesOfferModule {
    ...SalesOfferModuleWithPermissionsSpecs
  }
  ... on VehicleDataWithPorscheCodeIntegrationModule {
    ...VehicleDataWithPorscheCodeIntegrationModuleWithPermissionsSpecs
  }
  ... on SalesControlBoardModule {
    ...SalesControlBoardModuleWithPermissionsSpecs
  }
  ... on OFRModule {
    ...OFRModuleSpecs
  }
}
    `;