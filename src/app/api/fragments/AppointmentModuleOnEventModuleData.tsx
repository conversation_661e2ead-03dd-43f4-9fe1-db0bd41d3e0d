import type * as SchemaTypes from '../types';

import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { gql } from '@apollo/client';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
export type AppointmentModuleOnEventModuleDataFragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName' | 'unavailableDayOfWeek' | 'advancedBookingLimit' | 'maxAdvancedBookingLimit' | 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'timeToSendReminder' | 'isReminderTimeEnabled'>
  & { bookingTimeSlot: Array<(
    { __typename: 'AppointmentTimeSlot' }
    & AppointmentTimeSlotDataFragment
  )>, bookingInformation?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, emailContents: (
    { __typename: 'AppointmentModuleEmailContents' }
    & AppointmentModuleEmailContentsSpecsFragment
  ), company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export const AppointmentModuleOnEventModuleDataFragmentDoc = /*#__PURE__*/ gql`
    fragment AppointmentModuleOnEventModuleData on AppointmentModule {
  id
  displayName
  unavailableDayOfWeek
  bookingTimeSlot {
    ...AppointmentTimeSlotData
  }
  advancedBookingLimit
  maxAdvancedBookingLimit
  bookingInformation {
    ...TranslatedStringData
  }
  hasTestDriveProcess
  hasTestDriveSigning
  timeToSendReminder
  isReminderTimeEnabled
  emailContents {
    ...AppointmentModuleEmailContentsSpecs
  }
  company {
    ...CompanyInModuleOptionData
    timeZone
    countryCode
  }
}
    `;