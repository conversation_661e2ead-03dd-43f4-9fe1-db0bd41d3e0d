import type * as SchemaTypes from '../types';

import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from '../fragments/ApplicationStageData';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { EventDataFragment } from '../fragments/EventData';
import type { EventModuleData_AdyenPaymentModule_Fragment, EventModuleData_AppointmentModule_Fragment, EventModuleData_AutoplayModule_Fragment, EventModuleData_BankModule_Fragment, EventModuleData_BasicSigningModule_Fragment, EventModuleData_CapModule_Fragment, EventModuleData_ConfiguratorModule_Fragment, EventModuleData_ConsentsAndDeclarationsModule_Fragment, EventModuleData_CtsModule_Fragment, EventModuleData_DocusignModule_Fragment, EventModuleData_EventApplicationModule_Fragment, EventModuleData_FinderApplicationPrivateModule_Fragment, EventModuleData_FinderApplicationPublicModule_Fragment, EventModuleData_FinderVehicleManagementModule_Fragment, EventModuleData_FiservPaymentModule_Fragment, EventModuleData_GiftVoucherModule_Fragment, EventModuleData_InsuranceModule_Fragment, EventModuleData_LabelsModule_Fragment, EventModuleData_LaunchPadModule_Fragment, EventModuleData_LocalCustomerManagementModule_Fragment, EventModuleData_MaintenanceModule_Fragment, EventModuleData_MarketingModule_Fragment, EventModuleData_MobilityModule_Fragment, EventModuleData_MyInfoModule_Fragment, EventModuleData_NamirialSigningModule_Fragment, EventModuleData_OfrModule_Fragment, EventModuleData_OidcModule_Fragment, EventModuleData_PayGatePaymentModule_Fragment, EventModuleData_PorscheIdModule_Fragment, EventModuleData_PorscheMasterDataModule_Fragment, EventModuleData_PorschePaymentModule_Fragment, EventModuleData_PorscheRetainModule_Fragment, EventModuleData_PromoCodeModule_Fragment, EventModuleData_SalesControlBoardModule_Fragment, EventModuleData_SalesOfferModule_Fragment, EventModuleData_SimpleVehicleManagementModule_Fragment, EventModuleData_StandardApplicationModule_Fragment, EventModuleData_TradeInModule_Fragment, EventModuleData_TtbPaymentModule_Fragment, EventModuleData_UserlikeChatbotModule_Fragment, EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, EventModuleData_VisitAppointmentModule_Fragment, EventModuleData_WebsiteModule_Fragment, EventModuleData_WhatsappLiveChatModule_Fragment } from '../fragments/EventModuleData';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import type { AppointmentModuleOnEventModuleDataFragment } from '../fragments/AppointmentModuleOnEventModuleData';
import type { AppointmentTimeSlotDataFragment } from '../fragments/AppointmentTimeSlotData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from '../fragments/AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from '../fragments/DealerBooleanSettingData';
import type { TimeSlotDataFragment } from '../fragments/TimeSlotData';
import type { EventApplicationModuleEmailContentSpecsFragment, EventEmailContentSpecsFragment } from '../fragments/EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from '../fragments/DealershipSettingSpecData';
import type { DepositAmountDataFragment } from '../fragments/DepositAmountData';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { KycPresetsSpecFragment } from '../fragments/KYCPresetsSpec';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { KycExtraSettingsSpecsFragment } from '../fragments/KYCExtraSettingsSpecs';
import type { CustomizedFieldDataFragment } from '../fragments/CustomizedFieldData';
import type { ThankYouPageContentSpecsFragment } from '../fragments/ThankYouPageContent';
import type { CustomTestDriveBookingSlotsDataFragment } from '../fragments/CustomTestDriveBookingSlotsData';
import type { TestDriveFixedPeriodDataFragment } from '../fragments/TestDriveFixedPeriodData';
import type { TestDriveBookingWindowSettingsDataFragment } from '../fragments/TestDriveBookingWindowSettingsData';
import type { UsersOptionsDataFragment } from '../fragments/UsersOptionsData';
import { gql } from '@apollo/client';
import { ApplicationStageDataFragmentDoc } from '../fragments/ApplicationStageData';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { EventDataFragmentDoc } from '../fragments/EventData';
import { EventModuleDataFragmentDoc } from '../fragments/EventModuleData';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import { AppointmentModuleOnEventModuleDataFragmentDoc } from '../fragments/AppointmentModuleOnEventModuleData';
import { AppointmentTimeSlotDataFragmentDoc } from '../fragments/AppointmentTimeSlotData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from '../fragments/AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from '../fragments/DealerBooleanSettingData';
import { TimeSlotDataFragmentDoc } from '../fragments/TimeSlotData';
import { EventApplicationModuleEmailContentSpecsFragmentDoc, EventEmailContentSpecsFragmentDoc } from '../fragments/EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { DealershipSettingSpecDataFragmentDoc } from '../fragments/DealershipSettingSpecData';
import { DepositAmountDataFragmentDoc } from '../fragments/DepositAmountData';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { KycPresetsSpecFragmentDoc } from '../fragments/KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { KycExtraSettingsSpecsFragmentDoc } from '../fragments/KYCExtraSettingsSpecs';
import { CustomizedFieldDataFragmentDoc } from '../fragments/CustomizedFieldData';
import { ThankYouPageContentSpecsFragmentDoc } from '../fragments/ThankYouPageContent';
import { CustomTestDriveBookingSlotsDataFragmentDoc } from '../fragments/CustomTestDriveBookingSlotsData';
import { TestDriveFixedPeriodDataFragmentDoc } from '../fragments/TestDriveFixedPeriodData';
import { TestDriveBookingWindowSettingsDataFragmentDoc } from '../fragments/TestDriveBookingWindowSettingsData';
import { UsersOptionsDataFragmentDoc } from '../fragments/UsersOptionsData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type DeleteUserMutationVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
  newAssigneeId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  companyId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type DeleteUserMutation = (
  { __typename: 'Mutation' }
  & { result: (
    { __typename: 'UserDeletionResult' }
    & Pick<SchemaTypes.UserDeletionResult, 'success' | 'message'>
    & { assigned?: SchemaTypes.Maybe<(
      { __typename: 'UserAssigned' }
      & { applications?: SchemaTypes.Maybe<Array<(
        { __typename: 'ConfiguratorApplication' }
        & { module: (
          { __typename: 'AdyenPaymentModule' }
          & Pick<SchemaTypes.AdyenPaymentModule, 'companyId'>
        ) | (
          { __typename: 'AppointmentModule' }
          & Pick<SchemaTypes.AppointmentModule, 'companyId'>
        ) | (
          { __typename: 'AutoplayModule' }
          & Pick<SchemaTypes.AutoplayModule, 'companyId'>
        ) | (
          { __typename: 'BankModule' }
          & Pick<SchemaTypes.BankModule, 'companyId'>
        ) | (
          { __typename: 'BasicSigningModule' }
          & Pick<SchemaTypes.BasicSigningModule, 'companyId'>
        ) | (
          { __typename: 'CapModule' }
          & Pick<SchemaTypes.CapModule, 'companyId'>
        ) | (
          { __typename: 'ConfiguratorModule' }
          & Pick<SchemaTypes.ConfiguratorModule, 'companyId'>
        ) | (
          { __typename: 'ConsentsAndDeclarationsModule' }
          & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'companyId'>
        ) | (
          { __typename: 'CtsModule' }
          & Pick<SchemaTypes.CtsModule, 'companyId'>
        ) | (
          { __typename: 'DocusignModule' }
          & Pick<SchemaTypes.DocusignModule, 'companyId'>
        ) | (
          { __typename: 'EventApplicationModule' }
          & Pick<SchemaTypes.EventApplicationModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPrivateModule' }
          & Pick<SchemaTypes.FinderApplicationPrivateModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPublicModule' }
          & Pick<SchemaTypes.FinderApplicationPublicModule, 'companyId'>
        ) | (
          { __typename: 'FinderVehicleManagementModule' }
          & Pick<SchemaTypes.FinderVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'FiservPaymentModule' }
          & Pick<SchemaTypes.FiservPaymentModule, 'companyId'>
        ) | (
          { __typename: 'GiftVoucherModule' }
          & Pick<SchemaTypes.GiftVoucherModule, 'companyId'>
        ) | (
          { __typename: 'InsuranceModule' }
          & Pick<SchemaTypes.InsuranceModule, 'companyId'>
        ) | (
          { __typename: 'LabelsModule' }
          & Pick<SchemaTypes.LabelsModule, 'companyId'>
        ) | (
          { __typename: 'LaunchPadModule' }
          & Pick<SchemaTypes.LaunchPadModule, 'companyId'>
        ) | (
          { __typename: 'LocalCustomerManagementModule' }
          & Pick<SchemaTypes.LocalCustomerManagementModule, 'companyId'>
        ) | (
          { __typename: 'MaintenanceModule' }
          & Pick<SchemaTypes.MaintenanceModule, 'companyId'>
        ) | (
          { __typename: 'MarketingModule' }
          & Pick<SchemaTypes.MarketingModule, 'companyId'>
        ) | (
          { __typename: 'MobilityModule' }
          & Pick<SchemaTypes.MobilityModule, 'companyId'>
        ) | (
          { __typename: 'MyInfoModule' }
          & Pick<SchemaTypes.MyInfoModule, 'companyId'>
        ) | (
          { __typename: 'NamirialSigningModule' }
          & Pick<SchemaTypes.NamirialSigningModule, 'companyId'>
        ) | (
          { __typename: 'OFRModule' }
          & Pick<SchemaTypes.OfrModule, 'companyId'>
        ) | (
          { __typename: 'OIDCModule' }
          & Pick<SchemaTypes.OidcModule, 'companyId'>
        ) | (
          { __typename: 'PayGatePaymentModule' }
          & Pick<SchemaTypes.PayGatePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheIdModule' }
          & Pick<SchemaTypes.PorscheIdModule, 'companyId'>
        ) | (
          { __typename: 'PorscheMasterDataModule' }
          & Pick<SchemaTypes.PorscheMasterDataModule, 'companyId'>
        ) | (
          { __typename: 'PorschePaymentModule' }
          & Pick<SchemaTypes.PorschePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheRetainModule' }
          & Pick<SchemaTypes.PorscheRetainModule, 'companyId'>
        ) | (
          { __typename: 'PromoCodeModule' }
          & Pick<SchemaTypes.PromoCodeModule, 'companyId'>
        ) | (
          { __typename: 'SalesControlBoardModule' }
          & Pick<SchemaTypes.SalesControlBoardModule, 'companyId'>
        ) | (
          { __typename: 'SalesOfferModule' }
          & Pick<SchemaTypes.SalesOfferModule, 'companyId'>
        ) | (
          { __typename: 'SimpleVehicleManagementModule' }
          & Pick<SchemaTypes.SimpleVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'StandardApplicationModule' }
          & Pick<SchemaTypes.StandardApplicationModule, 'companyId'>
        ) | (
          { __typename: 'TradeInModule' }
          & Pick<SchemaTypes.TradeInModule, 'companyId'>
        ) | (
          { __typename: 'TtbPaymentModule' }
          & Pick<SchemaTypes.TtbPaymentModule, 'companyId'>
        ) | (
          { __typename: 'UserlikeChatbotModule' }
          & Pick<SchemaTypes.UserlikeChatbotModule, 'companyId'>
        ) | (
          { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
          & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'companyId'>
        ) | (
          { __typename: 'VisitAppointmentModule' }
          & Pick<SchemaTypes.VisitAppointmentModule, 'companyId'>
        ) | (
          { __typename: 'WebsiteModule' }
          & Pick<SchemaTypes.WebsiteModule, 'companyId'>
        ) | (
          { __typename: 'WhatsappLiveChatModule' }
          & Pick<SchemaTypes.WhatsappLiveChatModule, 'companyId'>
        ), versioning: (
          { __typename: 'AdvancedVersioning' }
          & AdvancedVersioningDataFragment
        ) }
        & ApplicationStageData_ConfiguratorApplication_Fragment
      ) | (
        { __typename: 'EventApplication' }
        & { module: (
          { __typename: 'AdyenPaymentModule' }
          & Pick<SchemaTypes.AdyenPaymentModule, 'companyId'>
        ) | (
          { __typename: 'AppointmentModule' }
          & Pick<SchemaTypes.AppointmentModule, 'companyId'>
        ) | (
          { __typename: 'AutoplayModule' }
          & Pick<SchemaTypes.AutoplayModule, 'companyId'>
        ) | (
          { __typename: 'BankModule' }
          & Pick<SchemaTypes.BankModule, 'companyId'>
        ) | (
          { __typename: 'BasicSigningModule' }
          & Pick<SchemaTypes.BasicSigningModule, 'companyId'>
        ) | (
          { __typename: 'CapModule' }
          & Pick<SchemaTypes.CapModule, 'companyId'>
        ) | (
          { __typename: 'ConfiguratorModule' }
          & Pick<SchemaTypes.ConfiguratorModule, 'companyId'>
        ) | (
          { __typename: 'ConsentsAndDeclarationsModule' }
          & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'companyId'>
        ) | (
          { __typename: 'CtsModule' }
          & Pick<SchemaTypes.CtsModule, 'companyId'>
        ) | (
          { __typename: 'DocusignModule' }
          & Pick<SchemaTypes.DocusignModule, 'companyId'>
        ) | (
          { __typename: 'EventApplicationModule' }
          & Pick<SchemaTypes.EventApplicationModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPrivateModule' }
          & Pick<SchemaTypes.FinderApplicationPrivateModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPublicModule' }
          & Pick<SchemaTypes.FinderApplicationPublicModule, 'companyId'>
        ) | (
          { __typename: 'FinderVehicleManagementModule' }
          & Pick<SchemaTypes.FinderVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'FiservPaymentModule' }
          & Pick<SchemaTypes.FiservPaymentModule, 'companyId'>
        ) | (
          { __typename: 'GiftVoucherModule' }
          & Pick<SchemaTypes.GiftVoucherModule, 'companyId'>
        ) | (
          { __typename: 'InsuranceModule' }
          & Pick<SchemaTypes.InsuranceModule, 'companyId'>
        ) | (
          { __typename: 'LabelsModule' }
          & Pick<SchemaTypes.LabelsModule, 'companyId'>
        ) | (
          { __typename: 'LaunchPadModule' }
          & Pick<SchemaTypes.LaunchPadModule, 'companyId'>
        ) | (
          { __typename: 'LocalCustomerManagementModule' }
          & Pick<SchemaTypes.LocalCustomerManagementModule, 'companyId'>
        ) | (
          { __typename: 'MaintenanceModule' }
          & Pick<SchemaTypes.MaintenanceModule, 'companyId'>
        ) | (
          { __typename: 'MarketingModule' }
          & Pick<SchemaTypes.MarketingModule, 'companyId'>
        ) | (
          { __typename: 'MobilityModule' }
          & Pick<SchemaTypes.MobilityModule, 'companyId'>
        ) | (
          { __typename: 'MyInfoModule' }
          & Pick<SchemaTypes.MyInfoModule, 'companyId'>
        ) | (
          { __typename: 'NamirialSigningModule' }
          & Pick<SchemaTypes.NamirialSigningModule, 'companyId'>
        ) | (
          { __typename: 'OFRModule' }
          & Pick<SchemaTypes.OfrModule, 'companyId'>
        ) | (
          { __typename: 'OIDCModule' }
          & Pick<SchemaTypes.OidcModule, 'companyId'>
        ) | (
          { __typename: 'PayGatePaymentModule' }
          & Pick<SchemaTypes.PayGatePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheIdModule' }
          & Pick<SchemaTypes.PorscheIdModule, 'companyId'>
        ) | (
          { __typename: 'PorscheMasterDataModule' }
          & Pick<SchemaTypes.PorscheMasterDataModule, 'companyId'>
        ) | (
          { __typename: 'PorschePaymentModule' }
          & Pick<SchemaTypes.PorschePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheRetainModule' }
          & Pick<SchemaTypes.PorscheRetainModule, 'companyId'>
        ) | (
          { __typename: 'PromoCodeModule' }
          & Pick<SchemaTypes.PromoCodeModule, 'companyId'>
        ) | (
          { __typename: 'SalesControlBoardModule' }
          & Pick<SchemaTypes.SalesControlBoardModule, 'companyId'>
        ) | (
          { __typename: 'SalesOfferModule' }
          & Pick<SchemaTypes.SalesOfferModule, 'companyId'>
        ) | (
          { __typename: 'SimpleVehicleManagementModule' }
          & Pick<SchemaTypes.SimpleVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'StandardApplicationModule' }
          & Pick<SchemaTypes.StandardApplicationModule, 'companyId'>
        ) | (
          { __typename: 'TradeInModule' }
          & Pick<SchemaTypes.TradeInModule, 'companyId'>
        ) | (
          { __typename: 'TtbPaymentModule' }
          & Pick<SchemaTypes.TtbPaymentModule, 'companyId'>
        ) | (
          { __typename: 'UserlikeChatbotModule' }
          & Pick<SchemaTypes.UserlikeChatbotModule, 'companyId'>
        ) | (
          { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
          & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'companyId'>
        ) | (
          { __typename: 'VisitAppointmentModule' }
          & Pick<SchemaTypes.VisitAppointmentModule, 'companyId'>
        ) | (
          { __typename: 'WebsiteModule' }
          & Pick<SchemaTypes.WebsiteModule, 'companyId'>
        ) | (
          { __typename: 'WhatsappLiveChatModule' }
          & Pick<SchemaTypes.WhatsappLiveChatModule, 'companyId'>
        ), versioning: (
          { __typename: 'AdvancedVersioning' }
          & AdvancedVersioningDataFragment
        ) }
        & ApplicationStageData_EventApplication_Fragment
      ) | (
        { __typename: 'FinderApplication' }
        & { module: (
          { __typename: 'AdyenPaymentModule' }
          & Pick<SchemaTypes.AdyenPaymentModule, 'companyId'>
        ) | (
          { __typename: 'AppointmentModule' }
          & Pick<SchemaTypes.AppointmentModule, 'companyId'>
        ) | (
          { __typename: 'AutoplayModule' }
          & Pick<SchemaTypes.AutoplayModule, 'companyId'>
        ) | (
          { __typename: 'BankModule' }
          & Pick<SchemaTypes.BankModule, 'companyId'>
        ) | (
          { __typename: 'BasicSigningModule' }
          & Pick<SchemaTypes.BasicSigningModule, 'companyId'>
        ) | (
          { __typename: 'CapModule' }
          & Pick<SchemaTypes.CapModule, 'companyId'>
        ) | (
          { __typename: 'ConfiguratorModule' }
          & Pick<SchemaTypes.ConfiguratorModule, 'companyId'>
        ) | (
          { __typename: 'ConsentsAndDeclarationsModule' }
          & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'companyId'>
        ) | (
          { __typename: 'CtsModule' }
          & Pick<SchemaTypes.CtsModule, 'companyId'>
        ) | (
          { __typename: 'DocusignModule' }
          & Pick<SchemaTypes.DocusignModule, 'companyId'>
        ) | (
          { __typename: 'EventApplicationModule' }
          & Pick<SchemaTypes.EventApplicationModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPrivateModule' }
          & Pick<SchemaTypes.FinderApplicationPrivateModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPublicModule' }
          & Pick<SchemaTypes.FinderApplicationPublicModule, 'companyId'>
        ) | (
          { __typename: 'FinderVehicleManagementModule' }
          & Pick<SchemaTypes.FinderVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'FiservPaymentModule' }
          & Pick<SchemaTypes.FiservPaymentModule, 'companyId'>
        ) | (
          { __typename: 'GiftVoucherModule' }
          & Pick<SchemaTypes.GiftVoucherModule, 'companyId'>
        ) | (
          { __typename: 'InsuranceModule' }
          & Pick<SchemaTypes.InsuranceModule, 'companyId'>
        ) | (
          { __typename: 'LabelsModule' }
          & Pick<SchemaTypes.LabelsModule, 'companyId'>
        ) | (
          { __typename: 'LaunchPadModule' }
          & Pick<SchemaTypes.LaunchPadModule, 'companyId'>
        ) | (
          { __typename: 'LocalCustomerManagementModule' }
          & Pick<SchemaTypes.LocalCustomerManagementModule, 'companyId'>
        ) | (
          { __typename: 'MaintenanceModule' }
          & Pick<SchemaTypes.MaintenanceModule, 'companyId'>
        ) | (
          { __typename: 'MarketingModule' }
          & Pick<SchemaTypes.MarketingModule, 'companyId'>
        ) | (
          { __typename: 'MobilityModule' }
          & Pick<SchemaTypes.MobilityModule, 'companyId'>
        ) | (
          { __typename: 'MyInfoModule' }
          & Pick<SchemaTypes.MyInfoModule, 'companyId'>
        ) | (
          { __typename: 'NamirialSigningModule' }
          & Pick<SchemaTypes.NamirialSigningModule, 'companyId'>
        ) | (
          { __typename: 'OFRModule' }
          & Pick<SchemaTypes.OfrModule, 'companyId'>
        ) | (
          { __typename: 'OIDCModule' }
          & Pick<SchemaTypes.OidcModule, 'companyId'>
        ) | (
          { __typename: 'PayGatePaymentModule' }
          & Pick<SchemaTypes.PayGatePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheIdModule' }
          & Pick<SchemaTypes.PorscheIdModule, 'companyId'>
        ) | (
          { __typename: 'PorscheMasterDataModule' }
          & Pick<SchemaTypes.PorscheMasterDataModule, 'companyId'>
        ) | (
          { __typename: 'PorschePaymentModule' }
          & Pick<SchemaTypes.PorschePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheRetainModule' }
          & Pick<SchemaTypes.PorscheRetainModule, 'companyId'>
        ) | (
          { __typename: 'PromoCodeModule' }
          & Pick<SchemaTypes.PromoCodeModule, 'companyId'>
        ) | (
          { __typename: 'SalesControlBoardModule' }
          & Pick<SchemaTypes.SalesControlBoardModule, 'companyId'>
        ) | (
          { __typename: 'SalesOfferModule' }
          & Pick<SchemaTypes.SalesOfferModule, 'companyId'>
        ) | (
          { __typename: 'SimpleVehicleManagementModule' }
          & Pick<SchemaTypes.SimpleVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'StandardApplicationModule' }
          & Pick<SchemaTypes.StandardApplicationModule, 'companyId'>
        ) | (
          { __typename: 'TradeInModule' }
          & Pick<SchemaTypes.TradeInModule, 'companyId'>
        ) | (
          { __typename: 'TtbPaymentModule' }
          & Pick<SchemaTypes.TtbPaymentModule, 'companyId'>
        ) | (
          { __typename: 'UserlikeChatbotModule' }
          & Pick<SchemaTypes.UserlikeChatbotModule, 'companyId'>
        ) | (
          { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
          & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'companyId'>
        ) | (
          { __typename: 'VisitAppointmentModule' }
          & Pick<SchemaTypes.VisitAppointmentModule, 'companyId'>
        ) | (
          { __typename: 'WebsiteModule' }
          & Pick<SchemaTypes.WebsiteModule, 'companyId'>
        ) | (
          { __typename: 'WhatsappLiveChatModule' }
          & Pick<SchemaTypes.WhatsappLiveChatModule, 'companyId'>
        ), versioning: (
          { __typename: 'AdvancedVersioning' }
          & AdvancedVersioningDataFragment
        ) }
        & ApplicationStageData_FinderApplication_Fragment
      ) | (
        { __typename: 'LaunchpadApplication' }
        & { module: (
          { __typename: 'AdyenPaymentModule' }
          & Pick<SchemaTypes.AdyenPaymentModule, 'companyId'>
        ) | (
          { __typename: 'AppointmentModule' }
          & Pick<SchemaTypes.AppointmentModule, 'companyId'>
        ) | (
          { __typename: 'AutoplayModule' }
          & Pick<SchemaTypes.AutoplayModule, 'companyId'>
        ) | (
          { __typename: 'BankModule' }
          & Pick<SchemaTypes.BankModule, 'companyId'>
        ) | (
          { __typename: 'BasicSigningModule' }
          & Pick<SchemaTypes.BasicSigningModule, 'companyId'>
        ) | (
          { __typename: 'CapModule' }
          & Pick<SchemaTypes.CapModule, 'companyId'>
        ) | (
          { __typename: 'ConfiguratorModule' }
          & Pick<SchemaTypes.ConfiguratorModule, 'companyId'>
        ) | (
          { __typename: 'ConsentsAndDeclarationsModule' }
          & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'companyId'>
        ) | (
          { __typename: 'CtsModule' }
          & Pick<SchemaTypes.CtsModule, 'companyId'>
        ) | (
          { __typename: 'DocusignModule' }
          & Pick<SchemaTypes.DocusignModule, 'companyId'>
        ) | (
          { __typename: 'EventApplicationModule' }
          & Pick<SchemaTypes.EventApplicationModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPrivateModule' }
          & Pick<SchemaTypes.FinderApplicationPrivateModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPublicModule' }
          & Pick<SchemaTypes.FinderApplicationPublicModule, 'companyId'>
        ) | (
          { __typename: 'FinderVehicleManagementModule' }
          & Pick<SchemaTypes.FinderVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'FiservPaymentModule' }
          & Pick<SchemaTypes.FiservPaymentModule, 'companyId'>
        ) | (
          { __typename: 'GiftVoucherModule' }
          & Pick<SchemaTypes.GiftVoucherModule, 'companyId'>
        ) | (
          { __typename: 'InsuranceModule' }
          & Pick<SchemaTypes.InsuranceModule, 'companyId'>
        ) | (
          { __typename: 'LabelsModule' }
          & Pick<SchemaTypes.LabelsModule, 'companyId'>
        ) | (
          { __typename: 'LaunchPadModule' }
          & Pick<SchemaTypes.LaunchPadModule, 'companyId'>
        ) | (
          { __typename: 'LocalCustomerManagementModule' }
          & Pick<SchemaTypes.LocalCustomerManagementModule, 'companyId'>
        ) | (
          { __typename: 'MaintenanceModule' }
          & Pick<SchemaTypes.MaintenanceModule, 'companyId'>
        ) | (
          { __typename: 'MarketingModule' }
          & Pick<SchemaTypes.MarketingModule, 'companyId'>
        ) | (
          { __typename: 'MobilityModule' }
          & Pick<SchemaTypes.MobilityModule, 'companyId'>
        ) | (
          { __typename: 'MyInfoModule' }
          & Pick<SchemaTypes.MyInfoModule, 'companyId'>
        ) | (
          { __typename: 'NamirialSigningModule' }
          & Pick<SchemaTypes.NamirialSigningModule, 'companyId'>
        ) | (
          { __typename: 'OFRModule' }
          & Pick<SchemaTypes.OfrModule, 'companyId'>
        ) | (
          { __typename: 'OIDCModule' }
          & Pick<SchemaTypes.OidcModule, 'companyId'>
        ) | (
          { __typename: 'PayGatePaymentModule' }
          & Pick<SchemaTypes.PayGatePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheIdModule' }
          & Pick<SchemaTypes.PorscheIdModule, 'companyId'>
        ) | (
          { __typename: 'PorscheMasterDataModule' }
          & Pick<SchemaTypes.PorscheMasterDataModule, 'companyId'>
        ) | (
          { __typename: 'PorschePaymentModule' }
          & Pick<SchemaTypes.PorschePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheRetainModule' }
          & Pick<SchemaTypes.PorscheRetainModule, 'companyId'>
        ) | (
          { __typename: 'PromoCodeModule' }
          & Pick<SchemaTypes.PromoCodeModule, 'companyId'>
        ) | (
          { __typename: 'SalesControlBoardModule' }
          & Pick<SchemaTypes.SalesControlBoardModule, 'companyId'>
        ) | (
          { __typename: 'SalesOfferModule' }
          & Pick<SchemaTypes.SalesOfferModule, 'companyId'>
        ) | (
          { __typename: 'SimpleVehicleManagementModule' }
          & Pick<SchemaTypes.SimpleVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'StandardApplicationModule' }
          & Pick<SchemaTypes.StandardApplicationModule, 'companyId'>
        ) | (
          { __typename: 'TradeInModule' }
          & Pick<SchemaTypes.TradeInModule, 'companyId'>
        ) | (
          { __typename: 'TtbPaymentModule' }
          & Pick<SchemaTypes.TtbPaymentModule, 'companyId'>
        ) | (
          { __typename: 'UserlikeChatbotModule' }
          & Pick<SchemaTypes.UserlikeChatbotModule, 'companyId'>
        ) | (
          { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
          & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'companyId'>
        ) | (
          { __typename: 'VisitAppointmentModule' }
          & Pick<SchemaTypes.VisitAppointmentModule, 'companyId'>
        ) | (
          { __typename: 'WebsiteModule' }
          & Pick<SchemaTypes.WebsiteModule, 'companyId'>
        ) | (
          { __typename: 'WhatsappLiveChatModule' }
          & Pick<SchemaTypes.WhatsappLiveChatModule, 'companyId'>
        ), versioning: (
          { __typename: 'AdvancedVersioning' }
          & AdvancedVersioningDataFragment
        ) }
        & ApplicationStageData_LaunchpadApplication_Fragment
      ) | (
        { __typename: 'MobilityApplication' }
        & { module: (
          { __typename: 'AdyenPaymentModule' }
          & Pick<SchemaTypes.AdyenPaymentModule, 'companyId'>
        ) | (
          { __typename: 'AppointmentModule' }
          & Pick<SchemaTypes.AppointmentModule, 'companyId'>
        ) | (
          { __typename: 'AutoplayModule' }
          & Pick<SchemaTypes.AutoplayModule, 'companyId'>
        ) | (
          { __typename: 'BankModule' }
          & Pick<SchemaTypes.BankModule, 'companyId'>
        ) | (
          { __typename: 'BasicSigningModule' }
          & Pick<SchemaTypes.BasicSigningModule, 'companyId'>
        ) | (
          { __typename: 'CapModule' }
          & Pick<SchemaTypes.CapModule, 'companyId'>
        ) | (
          { __typename: 'ConfiguratorModule' }
          & Pick<SchemaTypes.ConfiguratorModule, 'companyId'>
        ) | (
          { __typename: 'ConsentsAndDeclarationsModule' }
          & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'companyId'>
        ) | (
          { __typename: 'CtsModule' }
          & Pick<SchemaTypes.CtsModule, 'companyId'>
        ) | (
          { __typename: 'DocusignModule' }
          & Pick<SchemaTypes.DocusignModule, 'companyId'>
        ) | (
          { __typename: 'EventApplicationModule' }
          & Pick<SchemaTypes.EventApplicationModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPrivateModule' }
          & Pick<SchemaTypes.FinderApplicationPrivateModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPublicModule' }
          & Pick<SchemaTypes.FinderApplicationPublicModule, 'companyId'>
        ) | (
          { __typename: 'FinderVehicleManagementModule' }
          & Pick<SchemaTypes.FinderVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'FiservPaymentModule' }
          & Pick<SchemaTypes.FiservPaymentModule, 'companyId'>
        ) | (
          { __typename: 'GiftVoucherModule' }
          & Pick<SchemaTypes.GiftVoucherModule, 'companyId'>
        ) | (
          { __typename: 'InsuranceModule' }
          & Pick<SchemaTypes.InsuranceModule, 'companyId'>
        ) | (
          { __typename: 'LabelsModule' }
          & Pick<SchemaTypes.LabelsModule, 'companyId'>
        ) | (
          { __typename: 'LaunchPadModule' }
          & Pick<SchemaTypes.LaunchPadModule, 'companyId'>
        ) | (
          { __typename: 'LocalCustomerManagementModule' }
          & Pick<SchemaTypes.LocalCustomerManagementModule, 'companyId'>
        ) | (
          { __typename: 'MaintenanceModule' }
          & Pick<SchemaTypes.MaintenanceModule, 'companyId'>
        ) | (
          { __typename: 'MarketingModule' }
          & Pick<SchemaTypes.MarketingModule, 'companyId'>
        ) | (
          { __typename: 'MobilityModule' }
          & Pick<SchemaTypes.MobilityModule, 'companyId'>
        ) | (
          { __typename: 'MyInfoModule' }
          & Pick<SchemaTypes.MyInfoModule, 'companyId'>
        ) | (
          { __typename: 'NamirialSigningModule' }
          & Pick<SchemaTypes.NamirialSigningModule, 'companyId'>
        ) | (
          { __typename: 'OFRModule' }
          & Pick<SchemaTypes.OfrModule, 'companyId'>
        ) | (
          { __typename: 'OIDCModule' }
          & Pick<SchemaTypes.OidcModule, 'companyId'>
        ) | (
          { __typename: 'PayGatePaymentModule' }
          & Pick<SchemaTypes.PayGatePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheIdModule' }
          & Pick<SchemaTypes.PorscheIdModule, 'companyId'>
        ) | (
          { __typename: 'PorscheMasterDataModule' }
          & Pick<SchemaTypes.PorscheMasterDataModule, 'companyId'>
        ) | (
          { __typename: 'PorschePaymentModule' }
          & Pick<SchemaTypes.PorschePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheRetainModule' }
          & Pick<SchemaTypes.PorscheRetainModule, 'companyId'>
        ) | (
          { __typename: 'PromoCodeModule' }
          & Pick<SchemaTypes.PromoCodeModule, 'companyId'>
        ) | (
          { __typename: 'SalesControlBoardModule' }
          & Pick<SchemaTypes.SalesControlBoardModule, 'companyId'>
        ) | (
          { __typename: 'SalesOfferModule' }
          & Pick<SchemaTypes.SalesOfferModule, 'companyId'>
        ) | (
          { __typename: 'SimpleVehicleManagementModule' }
          & Pick<SchemaTypes.SimpleVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'StandardApplicationModule' }
          & Pick<SchemaTypes.StandardApplicationModule, 'companyId'>
        ) | (
          { __typename: 'TradeInModule' }
          & Pick<SchemaTypes.TradeInModule, 'companyId'>
        ) | (
          { __typename: 'TtbPaymentModule' }
          & Pick<SchemaTypes.TtbPaymentModule, 'companyId'>
        ) | (
          { __typename: 'UserlikeChatbotModule' }
          & Pick<SchemaTypes.UserlikeChatbotModule, 'companyId'>
        ) | (
          { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
          & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'companyId'>
        ) | (
          { __typename: 'VisitAppointmentModule' }
          & Pick<SchemaTypes.VisitAppointmentModule, 'companyId'>
        ) | (
          { __typename: 'WebsiteModule' }
          & Pick<SchemaTypes.WebsiteModule, 'companyId'>
        ) | (
          { __typename: 'WhatsappLiveChatModule' }
          & Pick<SchemaTypes.WhatsappLiveChatModule, 'companyId'>
        ), versioning: (
          { __typename: 'AdvancedVersioning' }
          & AdvancedVersioningDataFragment
        ) }
        & ApplicationStageData_MobilityApplication_Fragment
      ) | (
        { __typename: 'SalesOfferApplication' }
        & { module: (
          { __typename: 'AdyenPaymentModule' }
          & Pick<SchemaTypes.AdyenPaymentModule, 'companyId'>
        ) | (
          { __typename: 'AppointmentModule' }
          & Pick<SchemaTypes.AppointmentModule, 'companyId'>
        ) | (
          { __typename: 'AutoplayModule' }
          & Pick<SchemaTypes.AutoplayModule, 'companyId'>
        ) | (
          { __typename: 'BankModule' }
          & Pick<SchemaTypes.BankModule, 'companyId'>
        ) | (
          { __typename: 'BasicSigningModule' }
          & Pick<SchemaTypes.BasicSigningModule, 'companyId'>
        ) | (
          { __typename: 'CapModule' }
          & Pick<SchemaTypes.CapModule, 'companyId'>
        ) | (
          { __typename: 'ConfiguratorModule' }
          & Pick<SchemaTypes.ConfiguratorModule, 'companyId'>
        ) | (
          { __typename: 'ConsentsAndDeclarationsModule' }
          & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'companyId'>
        ) | (
          { __typename: 'CtsModule' }
          & Pick<SchemaTypes.CtsModule, 'companyId'>
        ) | (
          { __typename: 'DocusignModule' }
          & Pick<SchemaTypes.DocusignModule, 'companyId'>
        ) | (
          { __typename: 'EventApplicationModule' }
          & Pick<SchemaTypes.EventApplicationModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPrivateModule' }
          & Pick<SchemaTypes.FinderApplicationPrivateModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPublicModule' }
          & Pick<SchemaTypes.FinderApplicationPublicModule, 'companyId'>
        ) | (
          { __typename: 'FinderVehicleManagementModule' }
          & Pick<SchemaTypes.FinderVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'FiservPaymentModule' }
          & Pick<SchemaTypes.FiservPaymentModule, 'companyId'>
        ) | (
          { __typename: 'GiftVoucherModule' }
          & Pick<SchemaTypes.GiftVoucherModule, 'companyId'>
        ) | (
          { __typename: 'InsuranceModule' }
          & Pick<SchemaTypes.InsuranceModule, 'companyId'>
        ) | (
          { __typename: 'LabelsModule' }
          & Pick<SchemaTypes.LabelsModule, 'companyId'>
        ) | (
          { __typename: 'LaunchPadModule' }
          & Pick<SchemaTypes.LaunchPadModule, 'companyId'>
        ) | (
          { __typename: 'LocalCustomerManagementModule' }
          & Pick<SchemaTypes.LocalCustomerManagementModule, 'companyId'>
        ) | (
          { __typename: 'MaintenanceModule' }
          & Pick<SchemaTypes.MaintenanceModule, 'companyId'>
        ) | (
          { __typename: 'MarketingModule' }
          & Pick<SchemaTypes.MarketingModule, 'companyId'>
        ) | (
          { __typename: 'MobilityModule' }
          & Pick<SchemaTypes.MobilityModule, 'companyId'>
        ) | (
          { __typename: 'MyInfoModule' }
          & Pick<SchemaTypes.MyInfoModule, 'companyId'>
        ) | (
          { __typename: 'NamirialSigningModule' }
          & Pick<SchemaTypes.NamirialSigningModule, 'companyId'>
        ) | (
          { __typename: 'OFRModule' }
          & Pick<SchemaTypes.OfrModule, 'companyId'>
        ) | (
          { __typename: 'OIDCModule' }
          & Pick<SchemaTypes.OidcModule, 'companyId'>
        ) | (
          { __typename: 'PayGatePaymentModule' }
          & Pick<SchemaTypes.PayGatePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheIdModule' }
          & Pick<SchemaTypes.PorscheIdModule, 'companyId'>
        ) | (
          { __typename: 'PorscheMasterDataModule' }
          & Pick<SchemaTypes.PorscheMasterDataModule, 'companyId'>
        ) | (
          { __typename: 'PorschePaymentModule' }
          & Pick<SchemaTypes.PorschePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheRetainModule' }
          & Pick<SchemaTypes.PorscheRetainModule, 'companyId'>
        ) | (
          { __typename: 'PromoCodeModule' }
          & Pick<SchemaTypes.PromoCodeModule, 'companyId'>
        ) | (
          { __typename: 'SalesControlBoardModule' }
          & Pick<SchemaTypes.SalesControlBoardModule, 'companyId'>
        ) | (
          { __typename: 'SalesOfferModule' }
          & Pick<SchemaTypes.SalesOfferModule, 'companyId'>
        ) | (
          { __typename: 'SimpleVehicleManagementModule' }
          & Pick<SchemaTypes.SimpleVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'StandardApplicationModule' }
          & Pick<SchemaTypes.StandardApplicationModule, 'companyId'>
        ) | (
          { __typename: 'TradeInModule' }
          & Pick<SchemaTypes.TradeInModule, 'companyId'>
        ) | (
          { __typename: 'TtbPaymentModule' }
          & Pick<SchemaTypes.TtbPaymentModule, 'companyId'>
        ) | (
          { __typename: 'UserlikeChatbotModule' }
          & Pick<SchemaTypes.UserlikeChatbotModule, 'companyId'>
        ) | (
          { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
          & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'companyId'>
        ) | (
          { __typename: 'VisitAppointmentModule' }
          & Pick<SchemaTypes.VisitAppointmentModule, 'companyId'>
        ) | (
          { __typename: 'WebsiteModule' }
          & Pick<SchemaTypes.WebsiteModule, 'companyId'>
        ) | (
          { __typename: 'WhatsappLiveChatModule' }
          & Pick<SchemaTypes.WhatsappLiveChatModule, 'companyId'>
        ), versioning: (
          { __typename: 'AdvancedVersioning' }
          & AdvancedVersioningDataFragment
        ) }
        & ApplicationStageData_SalesOfferApplication_Fragment
      ) | (
        { __typename: 'StandardApplication' }
        & { module: (
          { __typename: 'AdyenPaymentModule' }
          & Pick<SchemaTypes.AdyenPaymentModule, 'companyId'>
        ) | (
          { __typename: 'AppointmentModule' }
          & Pick<SchemaTypes.AppointmentModule, 'companyId'>
        ) | (
          { __typename: 'AutoplayModule' }
          & Pick<SchemaTypes.AutoplayModule, 'companyId'>
        ) | (
          { __typename: 'BankModule' }
          & Pick<SchemaTypes.BankModule, 'companyId'>
        ) | (
          { __typename: 'BasicSigningModule' }
          & Pick<SchemaTypes.BasicSigningModule, 'companyId'>
        ) | (
          { __typename: 'CapModule' }
          & Pick<SchemaTypes.CapModule, 'companyId'>
        ) | (
          { __typename: 'ConfiguratorModule' }
          & Pick<SchemaTypes.ConfiguratorModule, 'companyId'>
        ) | (
          { __typename: 'ConsentsAndDeclarationsModule' }
          & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'companyId'>
        ) | (
          { __typename: 'CtsModule' }
          & Pick<SchemaTypes.CtsModule, 'companyId'>
        ) | (
          { __typename: 'DocusignModule' }
          & Pick<SchemaTypes.DocusignModule, 'companyId'>
        ) | (
          { __typename: 'EventApplicationModule' }
          & Pick<SchemaTypes.EventApplicationModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPrivateModule' }
          & Pick<SchemaTypes.FinderApplicationPrivateModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPublicModule' }
          & Pick<SchemaTypes.FinderApplicationPublicModule, 'companyId'>
        ) | (
          { __typename: 'FinderVehicleManagementModule' }
          & Pick<SchemaTypes.FinderVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'FiservPaymentModule' }
          & Pick<SchemaTypes.FiservPaymentModule, 'companyId'>
        ) | (
          { __typename: 'GiftVoucherModule' }
          & Pick<SchemaTypes.GiftVoucherModule, 'companyId'>
        ) | (
          { __typename: 'InsuranceModule' }
          & Pick<SchemaTypes.InsuranceModule, 'companyId'>
        ) | (
          { __typename: 'LabelsModule' }
          & Pick<SchemaTypes.LabelsModule, 'companyId'>
        ) | (
          { __typename: 'LaunchPadModule' }
          & Pick<SchemaTypes.LaunchPadModule, 'companyId'>
        ) | (
          { __typename: 'LocalCustomerManagementModule' }
          & Pick<SchemaTypes.LocalCustomerManagementModule, 'companyId'>
        ) | (
          { __typename: 'MaintenanceModule' }
          & Pick<SchemaTypes.MaintenanceModule, 'companyId'>
        ) | (
          { __typename: 'MarketingModule' }
          & Pick<SchemaTypes.MarketingModule, 'companyId'>
        ) | (
          { __typename: 'MobilityModule' }
          & Pick<SchemaTypes.MobilityModule, 'companyId'>
        ) | (
          { __typename: 'MyInfoModule' }
          & Pick<SchemaTypes.MyInfoModule, 'companyId'>
        ) | (
          { __typename: 'NamirialSigningModule' }
          & Pick<SchemaTypes.NamirialSigningModule, 'companyId'>
        ) | (
          { __typename: 'OFRModule' }
          & Pick<SchemaTypes.OfrModule, 'companyId'>
        ) | (
          { __typename: 'OIDCModule' }
          & Pick<SchemaTypes.OidcModule, 'companyId'>
        ) | (
          { __typename: 'PayGatePaymentModule' }
          & Pick<SchemaTypes.PayGatePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheIdModule' }
          & Pick<SchemaTypes.PorscheIdModule, 'companyId'>
        ) | (
          { __typename: 'PorscheMasterDataModule' }
          & Pick<SchemaTypes.PorscheMasterDataModule, 'companyId'>
        ) | (
          { __typename: 'PorschePaymentModule' }
          & Pick<SchemaTypes.PorschePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheRetainModule' }
          & Pick<SchemaTypes.PorscheRetainModule, 'companyId'>
        ) | (
          { __typename: 'PromoCodeModule' }
          & Pick<SchemaTypes.PromoCodeModule, 'companyId'>
        ) | (
          { __typename: 'SalesControlBoardModule' }
          & Pick<SchemaTypes.SalesControlBoardModule, 'companyId'>
        ) | (
          { __typename: 'SalesOfferModule' }
          & Pick<SchemaTypes.SalesOfferModule, 'companyId'>
        ) | (
          { __typename: 'SimpleVehicleManagementModule' }
          & Pick<SchemaTypes.SimpleVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'StandardApplicationModule' }
          & Pick<SchemaTypes.StandardApplicationModule, 'companyId'>
        ) | (
          { __typename: 'TradeInModule' }
          & Pick<SchemaTypes.TradeInModule, 'companyId'>
        ) | (
          { __typename: 'TtbPaymentModule' }
          & Pick<SchemaTypes.TtbPaymentModule, 'companyId'>
        ) | (
          { __typename: 'UserlikeChatbotModule' }
          & Pick<SchemaTypes.UserlikeChatbotModule, 'companyId'>
        ) | (
          { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
          & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'companyId'>
        ) | (
          { __typename: 'VisitAppointmentModule' }
          & Pick<SchemaTypes.VisitAppointmentModule, 'companyId'>
        ) | (
          { __typename: 'WebsiteModule' }
          & Pick<SchemaTypes.WebsiteModule, 'companyId'>
        ) | (
          { __typename: 'WhatsappLiveChatModule' }
          & Pick<SchemaTypes.WhatsappLiveChatModule, 'companyId'>
        ), versioning: (
          { __typename: 'AdvancedVersioning' }
          & AdvancedVersioningDataFragment
        ) }
        & ApplicationStageData_StandardApplication_Fragment
      )>>, modules?: SchemaTypes.Maybe<Array<(
        { __typename: 'AdyenPaymentModule' }
        & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'AppointmentModule' }
        & Pick<SchemaTypes.AppointmentModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'AutoplayModule' }
        & Pick<SchemaTypes.AutoplayModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'BankModule' }
        & Pick<SchemaTypes.BankModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'BasicSigningModule' }
        & Pick<SchemaTypes.BasicSigningModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'CapModule' }
        & Pick<SchemaTypes.CapModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'ConfiguratorModule' }
        & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'ConsentsAndDeclarationsModule' }
        & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'CtsModule' }
        & Pick<SchemaTypes.CtsModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'DocusignModule' }
        & Pick<SchemaTypes.DocusignModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'EventApplicationModule' }
        & Pick<SchemaTypes.EventApplicationModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'FinderApplicationPrivateModule' }
        & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'FinderApplicationPublicModule' }
        & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'FinderVehicleManagementModule' }
        & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'FiservPaymentModule' }
        & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'GiftVoucherModule' }
        & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'InsuranceModule' }
        & Pick<SchemaTypes.InsuranceModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'LabelsModule' }
        & Pick<SchemaTypes.LabelsModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'LaunchPadModule' }
        & Pick<SchemaTypes.LaunchPadModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'LocalCustomerManagementModule' }
        & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'MaintenanceModule' }
        & Pick<SchemaTypes.MaintenanceModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'MarketingModule' }
        & Pick<SchemaTypes.MarketingModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'MobilityModule' }
        & Pick<SchemaTypes.MobilityModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'MyInfoModule' }
        & Pick<SchemaTypes.MyInfoModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'NamirialSigningModule' }
        & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'OFRModule' }
        & Pick<SchemaTypes.OfrModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'OIDCModule' }
        & Pick<SchemaTypes.OidcModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'PayGatePaymentModule' }
        & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'PorscheIdModule' }
        & Pick<SchemaTypes.PorscheIdModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'PorscheMasterDataModule' }
        & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'PorschePaymentModule' }
        & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'PorscheRetainModule' }
        & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'PromoCodeModule' }
        & Pick<SchemaTypes.PromoCodeModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'SalesControlBoardModule' }
        & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'SalesOfferModule' }
        & Pick<SchemaTypes.SalesOfferModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'SimpleVehicleManagementModule' }
        & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'StandardApplicationModule' }
        & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'TradeInModule' }
        & Pick<SchemaTypes.TradeInModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'TtbPaymentModule' }
        & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'UserlikeChatbotModule' }
        & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
        & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'VisitAppointmentModule' }
        & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'WebsiteModule' }
        & Pick<SchemaTypes.WebsiteModule, 'id' | 'companyId' | 'displayName'>
      ) | (
        { __typename: 'WhatsappLiveChatModule' }
        & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'companyId' | 'displayName'>
      )>>, events?: SchemaTypes.Maybe<Array<(
        { __typename: 'Event' }
        & Pick<SchemaTypes.Event, 'displayName'>
        & { module: (
          { __typename: 'AdyenPaymentModule' }
          & Pick<SchemaTypes.AdyenPaymentModule, 'companyId'>
        ) | (
          { __typename: 'AppointmentModule' }
          & Pick<SchemaTypes.AppointmentModule, 'companyId'>
        ) | (
          { __typename: 'AutoplayModule' }
          & Pick<SchemaTypes.AutoplayModule, 'companyId'>
        ) | (
          { __typename: 'BankModule' }
          & Pick<SchemaTypes.BankModule, 'companyId'>
        ) | (
          { __typename: 'BasicSigningModule' }
          & Pick<SchemaTypes.BasicSigningModule, 'companyId'>
        ) | (
          { __typename: 'CapModule' }
          & Pick<SchemaTypes.CapModule, 'companyId'>
        ) | (
          { __typename: 'ConfiguratorModule' }
          & Pick<SchemaTypes.ConfiguratorModule, 'companyId'>
        ) | (
          { __typename: 'ConsentsAndDeclarationsModule' }
          & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'companyId'>
        ) | (
          { __typename: 'CtsModule' }
          & Pick<SchemaTypes.CtsModule, 'companyId'>
        ) | (
          { __typename: 'DocusignModule' }
          & Pick<SchemaTypes.DocusignModule, 'companyId'>
        ) | (
          { __typename: 'EventApplicationModule' }
          & Pick<SchemaTypes.EventApplicationModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPrivateModule' }
          & Pick<SchemaTypes.FinderApplicationPrivateModule, 'companyId'>
        ) | (
          { __typename: 'FinderApplicationPublicModule' }
          & Pick<SchemaTypes.FinderApplicationPublicModule, 'companyId'>
        ) | (
          { __typename: 'FinderVehicleManagementModule' }
          & Pick<SchemaTypes.FinderVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'FiservPaymentModule' }
          & Pick<SchemaTypes.FiservPaymentModule, 'companyId'>
        ) | (
          { __typename: 'GiftVoucherModule' }
          & Pick<SchemaTypes.GiftVoucherModule, 'companyId'>
        ) | (
          { __typename: 'InsuranceModule' }
          & Pick<SchemaTypes.InsuranceModule, 'companyId'>
        ) | (
          { __typename: 'LabelsModule' }
          & Pick<SchemaTypes.LabelsModule, 'companyId'>
        ) | (
          { __typename: 'LaunchPadModule' }
          & Pick<SchemaTypes.LaunchPadModule, 'companyId'>
        ) | (
          { __typename: 'LocalCustomerManagementModule' }
          & Pick<SchemaTypes.LocalCustomerManagementModule, 'companyId'>
        ) | (
          { __typename: 'MaintenanceModule' }
          & Pick<SchemaTypes.MaintenanceModule, 'companyId'>
        ) | (
          { __typename: 'MarketingModule' }
          & Pick<SchemaTypes.MarketingModule, 'companyId'>
        ) | (
          { __typename: 'MobilityModule' }
          & Pick<SchemaTypes.MobilityModule, 'companyId'>
        ) | (
          { __typename: 'MyInfoModule' }
          & Pick<SchemaTypes.MyInfoModule, 'companyId'>
        ) | (
          { __typename: 'NamirialSigningModule' }
          & Pick<SchemaTypes.NamirialSigningModule, 'companyId'>
        ) | (
          { __typename: 'OFRModule' }
          & Pick<SchemaTypes.OfrModule, 'companyId'>
        ) | (
          { __typename: 'OIDCModule' }
          & Pick<SchemaTypes.OidcModule, 'companyId'>
        ) | (
          { __typename: 'PayGatePaymentModule' }
          & Pick<SchemaTypes.PayGatePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheIdModule' }
          & Pick<SchemaTypes.PorscheIdModule, 'companyId'>
        ) | (
          { __typename: 'PorscheMasterDataModule' }
          & Pick<SchemaTypes.PorscheMasterDataModule, 'companyId'>
        ) | (
          { __typename: 'PorschePaymentModule' }
          & Pick<SchemaTypes.PorschePaymentModule, 'companyId'>
        ) | (
          { __typename: 'PorscheRetainModule' }
          & Pick<SchemaTypes.PorscheRetainModule, 'companyId'>
        ) | (
          { __typename: 'PromoCodeModule' }
          & Pick<SchemaTypes.PromoCodeModule, 'companyId'>
        ) | (
          { __typename: 'SalesControlBoardModule' }
          & Pick<SchemaTypes.SalesControlBoardModule, 'companyId'>
        ) | (
          { __typename: 'SalesOfferModule' }
          & Pick<SchemaTypes.SalesOfferModule, 'companyId'>
        ) | (
          { __typename: 'SimpleVehicleManagementModule' }
          & Pick<SchemaTypes.SimpleVehicleManagementModule, 'companyId'>
        ) | (
          { __typename: 'StandardApplicationModule' }
          & Pick<SchemaTypes.StandardApplicationModule, 'companyId'>
        ) | (
          { __typename: 'TradeInModule' }
          & Pick<SchemaTypes.TradeInModule, 'companyId'>
        ) | (
          { __typename: 'TtbPaymentModule' }
          & Pick<SchemaTypes.TtbPaymentModule, 'companyId'>
        ) | (
          { __typename: 'UserlikeChatbotModule' }
          & Pick<SchemaTypes.UserlikeChatbotModule, 'companyId'>
        ) | (
          { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
          & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'companyId'>
        ) | (
          { __typename: 'VisitAppointmentModule' }
          & Pick<SchemaTypes.VisitAppointmentModule, 'companyId'>
        ) | (
          { __typename: 'WebsiteModule' }
          & Pick<SchemaTypes.WebsiteModule, 'companyId'>
        ) | (
          { __typename: 'WhatsappLiveChatModule' }
          & Pick<SchemaTypes.WhatsappLiveChatModule, 'companyId'>
        ) }
        & EventDataFragment
      )>> }
    )>, availableAssignees?: SchemaTypes.Maybe<Array<SchemaTypes.Maybe<(
      { __typename: 'User' }
      & UsersOptionsDataFragment
    )>>> }
  ) }
);


export const DeleteUserDocument = /*#__PURE__*/ gql`
    mutation deleteUser($id: ObjectID!, $newAssigneeId: ObjectID, $companyId: ObjectID) {
  result: deleteUser(
    id: $id
    newAssigneeId: $newAssigneeId
    companyId: $companyId
  ) {
    success
    message
    assigned {
      applications {
        module {
          companyId
        }
        ...ApplicationStageData
        versioning {
          ...AdvancedVersioningData
        }
      }
      modules {
        id
        companyId
        displayName
      }
      events {
        ...EventData
        module {
          companyId
        }
        displayName
      }
    }
    availableAssignees {
      ...UsersOptionsData
    }
  }
}
    ${ApplicationStageDataFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${EventDataFragmentDoc}
${EventModuleDataFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${LocalCustomerManagementModuleKycFieldSpecsFragmentDoc}
${AppointmentModuleOnEventModuleDataFragmentDoc}
${AppointmentTimeSlotDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${AppointmentModuleEmailContentsSpecsFragmentDoc}
${AppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${AppointmentModuleEmailContentSpecsFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${DealerBooleanSettingDataFragmentDoc}
${AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc}
${TimeSlotDataFragmentDoc}
${EventApplicationModuleEmailContentSpecsFragmentDoc}
${TranslatedTextDataFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${DealershipSettingSpecDataFragmentDoc}
${DepositAmountDataFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${KycPresetsSpecFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${KycExtraSettingsSpecsFragmentDoc}
${CustomizedFieldDataFragmentDoc}
${EventEmailContentSpecsFragmentDoc}
${ThankYouPageContentSpecsFragmentDoc}
${CustomTestDriveBookingSlotsDataFragmentDoc}
${TestDriveFixedPeriodDataFragmentDoc}
${TestDriveBookingWindowSettingsDataFragmentDoc}
${UsersOptionsDataFragmentDoc}`;
export type DeleteUserMutationFn = Apollo.MutationFunction<DeleteUserMutation, DeleteUserMutationVariables>;

/**
 * __useDeleteUserMutation__
 *
 * To run a mutation, you first call `useDeleteUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteUserMutation, { data, loading, error }] = useDeleteUserMutation({
 *   variables: {
 *      id: // value for 'id'
 *      newAssigneeId: // value for 'newAssigneeId'
 *      companyId: // value for 'companyId'
 *   },
 * });
 */
export function useDeleteUserMutation(baseOptions?: Apollo.MutationHookOptions<DeleteUserMutation, DeleteUserMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteUserMutation, DeleteUserMutationVariables>(DeleteUserDocument, options);
      }
export type DeleteUserMutationHookResult = ReturnType<typeof useDeleteUserMutation>;
export type DeleteUserMutationResult = Apollo.MutationResult<DeleteUserMutation>;
export type DeleteUserMutationOptions = Apollo.BaseMutationOptions<DeleteUserMutation, DeleteUserMutationVariables>;