import type * as SchemaTypes from '../types';

import type { DebugJourneyDataFragment } from '../fragments/DebugJourneyData';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from '../fragments/ApplicationStageData';
import type { StandardApplicationSpecFragment } from '../fragments/StandardApplicationSpec';
import type { DealerJourneyDataFragment } from '../fragments/DealerJourneyData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { StandardApplicationModuleDebugJourneyFragment } from '../fragments/StandardApplicationModuleDebugJourney';
import type { AppointmentModuleApplicationJourneyFragment } from '../fragments/AppointmentModuleApplicationJourney';
import type { AppointmentTimeSlotDataFragment } from '../fragments/AppointmentTimeSlotData';
import type { NamirialSigningModuleSpecsFragment } from '../fragments/NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from '../fragments/NamirialSettingsSpec';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { VisitAppointmentModuleApplicationJourneyFragment } from '../fragments/VisitAppointmentModuleApplicationJourney';
import type { TimeSlotDataFragment } from '../fragments/TimeSlotData';
import type { KycExtraSettingsSpecsFragment } from '../fragments/KYCExtraSettingsSpecs';
import type { TradeInVehicleDataFragment } from '../fragments/TradeInVehicleData';
import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_GroupApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from '../fragments/ApplicationAgreementData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { MarketingPlatformSpecsFragment } from '../fragments/MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from '../fragments/MarketingPlatformsAgreedSpecs';
import type { UsersOptionsDataFragment } from '../fragments/UsersOptionsData';
import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import type { ApplicationDocumentDataFragment } from '../fragments/ApplicationDocumentData';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from '../fragments/CustomerSpecs';
import type { LocalCustomerDataFragment } from '../fragments/LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from '../fragments/LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from '../fragments/CorporateCustomerData';
import type { GuarantorDataFragment } from '../fragments/GuarantorData';
import type { JourneyDraftFlowFragment } from '../fragments/JourneyDraftFlow';
import type { ApplicationJourneyDeposit_ApplicationAdyenDeposit_Fragment, ApplicationJourneyDeposit_ApplicationFiservDeposit_Fragment, ApplicationJourneyDeposit_ApplicationPayGateDeposit_Fragment, ApplicationJourneyDeposit_ApplicationPorscheDeposit_Fragment, ApplicationJourneyDeposit_ApplicationTtbDeposit_Fragment } from '../fragments/ApplicationJourneyDeposit';
import type { ApplicationFinancingData_DefaultApplicationFinancing_Fragment, ApplicationFinancingData_NewZealandApplicationFinancing_Fragment, ApplicationFinancingData_SingaporeApplicationFinancing_Fragment } from '../fragments/ApplicationFinancingData';
import type { ApplicationInsurancingData_DefaultApplicationInsurancing_Fragment, ApplicationInsurancingData_NewZealandApplicationInsurancing_Fragment, ApplicationInsurancingData_SingaporeApplicationInsurancing_Fragment } from '../fragments/ApplicationInsurancingData';
import type { NamirialSigningDataFragment } from '../fragments/NamirialSigningData';
import type { ApplicationVariantSpecFragment } from '../fragments/ApplicationVehicleSpec';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { ApplicationModelSpecsFragment } from '../fragments/ApplicationModelSpec';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { ApplicationQuotationDataFragment, ApplicationQuotationOptionDataFragment } from '../fragments/ApplicationQuotationData';
import type { LeadData_ConfiguratorLead_Fragment, LeadData_EventLead_Fragment, LeadData_FinderLead_Fragment, LeadData_LaunchpadLead_Fragment, LeadData_MobilityLead_Fragment, LeadData_StandardLead_Fragment } from '../fragments/LeadData';
import type { StandardLeadDataFragment } from '../fragments/StandardLeadData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from '../fragments/VehicleSpecs';
import type { LocalVariantBaseSpecsFragment } from '../fragments/LocalVariantBaseSpecs';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { FinderVehicleSpecsFragment } from '../fragments/FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from '../fragments/finderListing.fragment';
import type { FinderLeadDataFragment } from '../fragments/FinderLeadData';
import type { EventLeadDataFragment } from '../fragments/EventLeadData';
import type { ApplicationEventCustomizedFieldDataFragment } from '../fragments/ApplicationEventCustomizedFieldData';
import type { LaunchpadLeadDataFragment } from '../fragments/LaunchpadLeadData';
import type { ConfiguratorLeadDataFragment } from '../fragments/ConfiguratorLeadData';
import type { ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment } from '../fragments/ConfiguratorJourneyBlocksData';
import type { BlockDetails_ColorBlock_Fragment, BlockDetails_OptionsBlock_Fragment, BlockDetails_PackageBlock_Fragment, BlockDetails_TrimBlock_Fragment } from '../fragments/BlockDetails';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from '../fragments/OptionSettingDetails';
import type { MobilityLeadDataFragment } from '../fragments/MobilityLeadData';
import type { LaunchpadModuleSpecsForApplicationFragment } from '../fragments/LaunchpadModuleSpecsForApplication';
import type { DealerVehiclesSpecsFragment } from '../fragments/DealerVehiclesSpecs';
import type { DealerApplicationFragmentFragment } from '../fragments/DealerApplicationFragment';
import type { DealerContactFragmentFragment } from '../fragments/DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from '../fragments/DealerSocialMediaFragment';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from '../fragments/ReferenceApplicationData';
import type { SalesOfferSpecsFragment } from '../fragments/SalesOfferSpecs';
import type { VehicleSalesOfferSpecsFragment } from '../fragments/VehicleSalesOfferSpecs';
import type { LocalVariantSpecsFragment } from '../fragments/LocalVariantSpecs';
import type { PorscheVehicleDataSpecsFragment, PorscheVehicleDataFeatureSpecsFragment, PorscheVehicleImagesSpecsFragment } from '../fragments/PorscheVehicleDataSpecs';
import type { LocalFittedOptionsSpecsFragment } from '../fragments/LocalFittedOptionsSpecs';
import type { SalesOfferDocumentDataFragment } from '../fragments/SalesOfferDocumentData';
import type { MainDetailsSalesOfferSpecsFragment } from '../fragments/MainDetailsSalesOfferSpecs';
import type { TradeInSalesOfferSpecsFragment } from '../fragments/TradeInSalesOfferSpecs';
import type { FinanceSalesOfferSpecsFragment } from '../fragments/FinanceSalesOfferSpecs';
import type { InsuranceSalesOfferSpecsFragment } from '../fragments/InsuranceSalesOfferSpecs';
import type { DepositSalesOfferSpecsFragment } from '../fragments/DepositSalesOfferSpecs';
import type { VsaSalesOfferSpecsFragment } from '../fragments/VSASalesOfferSpecs';
import type { SalesOfferModuleSpecsFragment } from '../fragments/SalesOfferModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from '../fragments/DealershipSettingSpecData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from '../fragments/ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from '../fragments/DealerMarketData';
import type { BankDealerMarketDataFragment } from '../fragments/BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from '../fragments/NzFeesDealerMarketData';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { BankDetailsDataFragment } from '../fragments/BankDetailsData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from '../fragments/BankIntegrationData';
import type { UploadFileFormDataFragment } from '../fragments/UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OfrModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from '../fragments/PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from '../fragments/LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from '../fragments/TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from '../fragments/InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from '../fragments/DownPaymentSettingsDetails';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from '../fragments/BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from '../fragments/BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from '../fragments/LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from '../fragments/DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from '../fragments/ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from '../fragments/LocalUcclLeasingOnlyDetails';
import type { CounterSettingsSpecsFragment } from '../fragments/CounterSettingsSpecs';
import type { DealerFinanceProductsSpecsFragment } from '../fragments/DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductListData';
import type { DealerInsuranceProductsSpecsFragment } from '../fragments/DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from '../fragments/InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from '../fragments/ErgoLookupTableSettingDetails';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import type { SalesOfferKycPresetSpecsFragment } from '../fragments/SalesOfferKYCPresetSpecs';
import type { SalesOfferConsentsSpecsFragment } from '../fragments/SalesOfferConsentsSpecs';
import type { SalesOfferSigningsSpecsFragment } from '../fragments/SalesOfferSigningsSpecs';
import type { EventApplicationSpecFragment } from '../fragments/EventApplicationSpec';
import type { JourneyEventDataFragment } from '../fragments/JourneyEventData';
import type { EventModuleData_AdyenPaymentModule_Fragment, EventModuleData_AppointmentModule_Fragment, EventModuleData_AutoplayModule_Fragment, EventModuleData_BankModule_Fragment, EventModuleData_BasicSigningModule_Fragment, EventModuleData_CapModule_Fragment, EventModuleData_ConfiguratorModule_Fragment, EventModuleData_ConsentsAndDeclarationsModule_Fragment, EventModuleData_CtsModule_Fragment, EventModuleData_DocusignModule_Fragment, EventModuleData_EventApplicationModule_Fragment, EventModuleData_FinderApplicationPrivateModule_Fragment, EventModuleData_FinderApplicationPublicModule_Fragment, EventModuleData_FinderVehicleManagementModule_Fragment, EventModuleData_FiservPaymentModule_Fragment, EventModuleData_GiftVoucherModule_Fragment, EventModuleData_InsuranceModule_Fragment, EventModuleData_LabelsModule_Fragment, EventModuleData_LaunchPadModule_Fragment, EventModuleData_LocalCustomerManagementModule_Fragment, EventModuleData_MaintenanceModule_Fragment, EventModuleData_MarketingModule_Fragment, EventModuleData_MobilityModule_Fragment, EventModuleData_MyInfoModule_Fragment, EventModuleData_NamirialSigningModule_Fragment, EventModuleData_OfrModule_Fragment, EventModuleData_OidcModule_Fragment, EventModuleData_PayGatePaymentModule_Fragment, EventModuleData_PorscheIdModule_Fragment, EventModuleData_PorscheMasterDataModule_Fragment, EventModuleData_PorschePaymentModule_Fragment, EventModuleData_PorscheRetainModule_Fragment, EventModuleData_PromoCodeModule_Fragment, EventModuleData_SalesControlBoardModule_Fragment, EventModuleData_SalesOfferModule_Fragment, EventModuleData_SimpleVehicleManagementModule_Fragment, EventModuleData_StandardApplicationModule_Fragment, EventModuleData_TradeInModule_Fragment, EventModuleData_TtbPaymentModule_Fragment, EventModuleData_UserlikeChatbotModule_Fragment, EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, EventModuleData_VisitAppointmentModule_Fragment, EventModuleData_WebsiteModule_Fragment, EventModuleData_WhatsappLiveChatModule_Fragment } from '../fragments/EventModuleData';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import type { AppointmentModuleOnEventModuleDataFragment } from '../fragments/AppointmentModuleOnEventModuleData';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from '../fragments/AppointmentModuleEmailContentsSpecs';
import type { DealerBooleanSettingDataFragment } from '../fragments/DealerBooleanSettingData';
import type { EventApplicationModuleEmailContentSpecsFragment, EventEmailContentSpecsFragment } from '../fragments/EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { DepositAmountDataFragment } from '../fragments/DepositAmountData';
import type { KycPresetsSpecFragment } from '../fragments/KYCPresetsSpec';
import type { CustomizedFieldDataFragment } from '../fragments/CustomizedFieldData';
import type { ThankYouPageContentSpecsFragment } from '../fragments/ThankYouPageContent';
import type { CustomTestDriveBookingSlotsDataFragment } from '../fragments/CustomTestDriveBookingSlotsData';
import type { TestDriveFixedPeriodDataFragment } from '../fragments/TestDriveFixedPeriodData';
import type { TestDriveBookingWindowSettingsDataFragment } from '../fragments/TestDriveBookingWindowSettingsData';
import type { EventApplicationModuleDebugJourneyFragment } from '../fragments/EventApplicationModuleDebugJourney';
import type { ConfiguratorApplicationSpecFragment } from '../fragments/ConfiguratorApplicationSpec';
import type { LocalVariantPublicSpecsFragment } from '../fragments/LocalVariantPublicSpecs';
import type { LocalModelPublicSpecsFragment } from '../fragments/LocalModelPublicSpecs';
import type { LocalMakePublicSpecsFragment } from '../fragments/LocalMakePublicSpecs';
import type { VariantConfiguratorJourneyDataFragment } from '../fragments/VariantConfiguratorJourneyData';
import type { MatrixDataFragment } from '../fragments/MatrixData';
import type { InventoryDetailsPublicData_ConfiguratorInventory_Fragment, InventoryDetailsPublicData_MobilityInventory_Fragment } from '../fragments/InventoryDetailsPublicData';
import type { StockInventorySpecs_ConfiguratorStockInventory_Fragment, StockInventorySpecs_MobilityStockInventory_Fragment } from '../fragments/StockInventorySpecs';
import type { CompanyPublicSpecsFragment } from '../fragments/CompanyPublicSpecs';
import type { StockBlockingPeriodDataFragment } from '../fragments/StockBlockingPeriod';
import type { ConfiguratorInventoryPublicSpecsFragment } from '../fragments/ConfiguratorInventoryPublicSpecs';
import type { MobilityInventoryPublicSpecsFragment } from '../fragments/MobilityInventoryPublicSpecs';
import type { MobilityModuleSpecsFragment } from '../fragments/MobilityModuleSpecs';
import type { DealerBookingCodeSpecsFragment } from '../fragments/DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from '../fragments/MobilitySigningSettingSpecs';
import type { KycPresetsOptionsDataFragment } from '../fragments/KYCPresetsOptionsData';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from '../fragments/MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from '../fragments/MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from '../fragments/MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from '../fragments/MobilityHomeDeliveryData';
import type { ConfiguratorApplicationModuleDebugJourneyFragment } from '../fragments/ConfiguratorApplicationModuleDebugJourney';
import type { PromoCodeSpecFragment } from '../fragments/PromoCodeSpec';
import type { GiftPromoTypeDataFragment } from '../fragments/GiftPromoTypeData';
import type { DiscountPromoTypeDataFragment } from '../fragments/DiscountPromoTypeData';
import type { MobilityApplicationSpecsFragment } from '../fragments/MobilityApplicationSpecs';
import type { MobilitySnapshotData_MobilityAdditionalInfoSnapshot_Fragment, MobilitySnapshotData_MobilityAddonSnapshot_Fragment } from '../fragments/MobilitySnapshotData';
import type { MobilityBookingLocationHomeDataFragment } from '../fragments/MobilityBookingLocationHomeData';
import type { MobilityBookingLocationPickupDataFragment } from '../fragments/MobilityBookingLocationPickupData';
import type { PromoCodeDataForApplicationFragment } from '../fragments/PromoCodeDataForApplication';
import type { GiftVoucherCodeDataFragment } from '../fragments/GiftVoucherCodeData';
import type { FinderApplicationSpecsFragment } from '../fragments/FinderApplicationSpecs';
import type { ApplicationFinderVehicleSpecsFragment } from '../fragments/ApplicationFinderVehicleSpecs';
import type { FinderApplicationPublicModuleDebugJourneyFragment } from '../fragments/FinderApplicationPublicModuleDebugJourney';
import type { FinderApplicationPrivateModuleDebugJourneyFragment } from '../fragments/FinderApplicationPrivateModuleDebugJourney';
import type { CtsModuleSettingDataFragment } from '../fragments/CtsModuleSettingData';
import type { DealerPriceDisclaimerDataFragment } from '../fragments/DealerPriceDisclaimerData';
import type { FinderApplicationPublicModuleSpecsFragment } from '../fragments/FinderApplicationPublicModuleSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import type { ModuleDisclaimersDataFragment } from '../fragments/ModuleDisclaimersData';
import type { FinderApplicationPrivateModuleSpecsFragment } from '../fragments/FinderApplicationPrivateModuleSpecs';
import type { FlexibleDiscountDataFragment } from '../fragments/FlexibleDiscountData';
import type { LaunchpadApplicationSpecFragment } from '../fragments/LaunchpadApplicationSpec';
import type { LaunchpadModuleDebugJourneyFragment } from '../fragments/LaunchpadModuleDebugJourney';
import type { SalesOfferApplicationSpecsFragment } from '../fragments/SalesOfferApplicationSpecs';
import type { SalesOfferModuleDebugJourneyFragment } from '../fragments/SalesOfferModuleDebugJourney';
import type { ApplicationAdyenDepositDataFragment } from '../fragments/ApplicationAdyenDepositData';
import type { ApplicationPorscheDepositDataFragment } from '../fragments/ApplicationPorscheDepositData';
import type { ApplicationFiservDepositDataFragment } from '../fragments/ApplicationFiservDepositData';
import type { ApplicationPayGateDepositDataFragment } from '../fragments/ApplicationPayGateDepositData';
import type { ApplicationTtbDepositDataFragment } from '../fragments/ApplicationTtbDepositData';
import { gql } from '@apollo/client';
import { DebugJourneyDataFragmentDoc } from '../fragments/DebugJourneyData';
import { ApplicationStageDataFragmentDoc } from '../fragments/ApplicationStageData';
import { StandardApplicationSpecFragmentDoc } from '../fragments/StandardApplicationSpec';
import { DealerJourneyDataFragmentDoc } from '../fragments/DealerJourneyData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { StandardApplicationModuleDebugJourneyFragmentDoc } from '../fragments/StandardApplicationModuleDebugJourney';
import { AppointmentModuleApplicationJourneyFragmentDoc } from '../fragments/AppointmentModuleApplicationJourney';
import { AppointmentTimeSlotDataFragmentDoc } from '../fragments/AppointmentTimeSlotData';
import { NamirialSigningModuleSpecsFragmentDoc } from '../fragments/NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from '../fragments/NamirialSettingsSpec';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { VisitAppointmentModuleApplicationJourneyFragmentDoc } from '../fragments/VisitAppointmentModuleApplicationJourney';
import { TimeSlotDataFragmentDoc } from '../fragments/TimeSlotData';
import { KycExtraSettingsSpecsFragmentDoc } from '../fragments/KYCExtraSettingsSpecs';
import { TradeInVehicleDataFragmentDoc } from '../fragments/TradeInVehicleData';
import { ApplicationAgreementDataFragmentDoc } from '../fragments/ApplicationAgreementData';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from '../fragments/MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from '../fragments/MarketingPlatformsAgreedSpecs';
import { UsersOptionsDataFragmentDoc } from '../fragments/UsersOptionsData';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import { ApplicationDocumentDataFragmentDoc } from '../fragments/ApplicationDocumentData';
import { CustomerSpecsFragmentDoc } from '../fragments/CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from '../fragments/LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from '../fragments/LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from '../fragments/CorporateCustomerData';
import { GuarantorDataFragmentDoc } from '../fragments/GuarantorData';
import { JourneyDraftFlowFragmentDoc } from '../fragments/JourneyDraftFlow';
import { ApplicationJourneyDepositFragmentDoc } from '../fragments/ApplicationJourneyDeposit';
import { ApplicationFinancingDataFragmentDoc } from '../fragments/ApplicationFinancingData';
import { ApplicationInsurancingDataFragmentDoc } from '../fragments/ApplicationInsurancingData';
import { NamirialSigningDataFragmentDoc } from '../fragments/NamirialSigningData';
import { ApplicationVariantSpecFragmentDoc } from '../fragments/ApplicationVehicleSpec';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { ApplicationModelSpecsFragmentDoc } from '../fragments/ApplicationModelSpec';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { ApplicationQuotationDataFragmentDoc, ApplicationQuotationOptionDataFragmentDoc } from '../fragments/ApplicationQuotationData';
import { LeadDataFragmentDoc } from '../fragments/LeadData';
import { StandardLeadDataFragmentDoc } from '../fragments/StandardLeadData';
import { VehicleSpecsFragmentDoc } from '../fragments/VehicleSpecs';
import { LocalVariantBaseSpecsFragmentDoc } from '../fragments/LocalVariantBaseSpecs';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { FinderVehicleSpecsFragmentDoc } from '../fragments/FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from '../fragments/finderListing.fragment';
import { FinderLeadDataFragmentDoc } from '../fragments/FinderLeadData';
import { EventLeadDataFragmentDoc } from '../fragments/EventLeadData';
import { ApplicationEventCustomizedFieldDataFragmentDoc } from '../fragments/ApplicationEventCustomizedFieldData';
import { LaunchpadLeadDataFragmentDoc } from '../fragments/LaunchpadLeadData';
import { ConfiguratorLeadDataFragmentDoc } from '../fragments/ConfiguratorLeadData';
import { ConfiguratorJourneyBlocksDataFragmentDoc } from '../fragments/ConfiguratorJourneyBlocksData';
import { BlockDetailsFragmentDoc } from '../fragments/BlockDetails';
import { OptionSettingDetailsFragmentDoc } from '../fragments/OptionSettingDetails';
import { MobilityLeadDataFragmentDoc } from '../fragments/MobilityLeadData';
import { LaunchpadModuleSpecsForApplicationFragmentDoc } from '../fragments/LaunchpadModuleSpecsForApplication';
import { DealerVehiclesSpecsFragmentDoc } from '../fragments/DealerVehiclesSpecs';
import { DealerApplicationFragmentFragmentDoc } from '../fragments/DealerApplicationFragment';
import { DealerContactFragmentFragmentDoc } from '../fragments/DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from '../fragments/DealerSocialMediaFragment';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from '../fragments/ReferenceApplicationData';
import { SalesOfferSpecsFragmentDoc } from '../fragments/SalesOfferSpecs';
import { VehicleSalesOfferSpecsFragmentDoc } from '../fragments/VehicleSalesOfferSpecs';
import { LocalVariantSpecsFragmentDoc } from '../fragments/LocalVariantSpecs';
import { PorscheVehicleDataSpecsFragmentDoc, PorscheVehicleDataFeatureSpecsFragmentDoc, PorscheVehicleImagesSpecsFragmentDoc } from '../fragments/PorscheVehicleDataSpecs';
import { LocalFittedOptionsSpecsFragmentDoc } from '../fragments/LocalFittedOptionsSpecs';
import { SalesOfferDocumentDataFragmentDoc } from '../fragments/SalesOfferDocumentData';
import { MainDetailsSalesOfferSpecsFragmentDoc } from '../fragments/MainDetailsSalesOfferSpecs';
import { TradeInSalesOfferSpecsFragmentDoc } from '../fragments/TradeInSalesOfferSpecs';
import { FinanceSalesOfferSpecsFragmentDoc } from '../fragments/FinanceSalesOfferSpecs';
import { InsuranceSalesOfferSpecsFragmentDoc } from '../fragments/InsuranceSalesOfferSpecs';
import { DepositSalesOfferSpecsFragmentDoc } from '../fragments/DepositSalesOfferSpecs';
import { VsaSalesOfferSpecsFragmentDoc } from '../fragments/VSASalesOfferSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from '../fragments/SalesOfferModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { DealershipSettingSpecDataFragmentDoc } from '../fragments/DealershipSettingSpecData';
import { ApplicationMarketTypeFragmentFragmentDoc } from '../fragments/ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from '../fragments/DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from '../fragments/BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from '../fragments/NzFeesDealerMarketData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { BankDetailsDataFragmentDoc } from '../fragments/BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from '../fragments/BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from '../fragments/UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from '../fragments/FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from '../fragments/PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from '../fragments/LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from '../fragments/TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from '../fragments/InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from '../fragments/DownPaymentSettingsDetails';
import { BalloonSettingsDetailsFragmentDoc } from '../fragments/BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from '../fragments/BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from '../fragments/LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from '../fragments/DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from '../fragments/ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from '../fragments/LocalUcclLeasingOnlyDetails';
import { CounterSettingsSpecsFragmentDoc } from '../fragments/CounterSettingsSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from '../fragments/DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from '../fragments/FinanceProductListData';
import { DealerInsuranceProductsSpecsFragmentDoc } from '../fragments/DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from '../fragments/InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from '../fragments/ErgoLookupTableSettingDetails';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import { SalesOfferKycPresetSpecsFragmentDoc } from '../fragments/SalesOfferKYCPresetSpecs';
import { SalesOfferConsentsSpecsFragmentDoc } from '../fragments/SalesOfferConsentsSpecs';
import { SalesOfferSigningsSpecsFragmentDoc } from '../fragments/SalesOfferSigningsSpecs';
import { EventApplicationSpecFragmentDoc } from '../fragments/EventApplicationSpec';
import { JourneyEventDataFragmentDoc } from '../fragments/JourneyEventData';
import { EventModuleDataFragmentDoc } from '../fragments/EventModuleData';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import { AppointmentModuleOnEventModuleDataFragmentDoc } from '../fragments/AppointmentModuleOnEventModuleData';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from '../fragments/AppointmentModuleEmailContentsSpecs';
import { DealerBooleanSettingDataFragmentDoc } from '../fragments/DealerBooleanSettingData';
import { EventApplicationModuleEmailContentSpecsFragmentDoc, EventEmailContentSpecsFragmentDoc } from '../fragments/EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { DepositAmountDataFragmentDoc } from '../fragments/DepositAmountData';
import { KycPresetsSpecFragmentDoc } from '../fragments/KYCPresetsSpec';
import { CustomizedFieldDataFragmentDoc } from '../fragments/CustomizedFieldData';
import { ThankYouPageContentSpecsFragmentDoc } from '../fragments/ThankYouPageContent';
import { CustomTestDriveBookingSlotsDataFragmentDoc } from '../fragments/CustomTestDriveBookingSlotsData';
import { TestDriveFixedPeriodDataFragmentDoc } from '../fragments/TestDriveFixedPeriodData';
import { TestDriveBookingWindowSettingsDataFragmentDoc } from '../fragments/TestDriveBookingWindowSettingsData';
import { EventApplicationModuleDebugJourneyFragmentDoc } from '../fragments/EventApplicationModuleDebugJourney';
import { ConfiguratorApplicationSpecFragmentDoc } from '../fragments/ConfiguratorApplicationSpec';
import { LocalVariantPublicSpecsFragmentDoc } from '../fragments/LocalVariantPublicSpecs';
import { LocalModelPublicSpecsFragmentDoc } from '../fragments/LocalModelPublicSpecs';
import { LocalMakePublicSpecsFragmentDoc } from '../fragments/LocalMakePublicSpecs';
import { VariantConfiguratorJourneyDataFragmentDoc } from '../fragments/VariantConfiguratorJourneyData';
import { MatrixDataFragmentDoc } from '../fragments/MatrixData';
import { InventoryDetailsPublicDataFragmentDoc } from '../fragments/InventoryDetailsPublicData';
import { StockInventorySpecsFragmentDoc } from '../fragments/StockInventorySpecs';
import { CompanyPublicSpecsFragmentDoc } from '../fragments/CompanyPublicSpecs';
import { StockBlockingPeriodDataFragmentDoc } from '../fragments/StockBlockingPeriod';
import { ConfiguratorInventoryPublicSpecsFragmentDoc } from '../fragments/ConfiguratorInventoryPublicSpecs';
import { MobilityInventoryPublicSpecsFragmentDoc } from '../fragments/MobilityInventoryPublicSpecs';
import { MobilityModuleSpecsFragmentDoc } from '../fragments/MobilityModuleSpecs';
import { DealerBookingCodeSpecsFragmentDoc } from '../fragments/DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from '../fragments/MobilitySigningSettingSpecs';
import { KycPresetsOptionsDataFragmentDoc } from '../fragments/KYCPresetsOptionsData';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from '../fragments/MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from '../fragments/MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from '../fragments/MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from '../fragments/MobilityHomeDeliveryData';
import { ConfiguratorApplicationModuleDebugJourneyFragmentDoc } from '../fragments/ConfiguratorApplicationModuleDebugJourney';
import { PromoCodeSpecFragmentDoc } from '../fragments/PromoCodeSpec';
import { GiftPromoTypeDataFragmentDoc } from '../fragments/GiftPromoTypeData';
import { DiscountPromoTypeDataFragmentDoc } from '../fragments/DiscountPromoTypeData';
import { MobilityApplicationSpecsFragmentDoc } from '../fragments/MobilityApplicationSpecs';
import { MobilitySnapshotDataFragmentDoc } from '../fragments/MobilitySnapshotData';
import { MobilityBookingLocationHomeDataFragmentDoc } from '../fragments/MobilityBookingLocationHomeData';
import { MobilityBookingLocationPickupDataFragmentDoc } from '../fragments/MobilityBookingLocationPickupData';
import { PromoCodeDataForApplicationFragmentDoc } from '../fragments/PromoCodeDataForApplication';
import { GiftVoucherCodeDataFragmentDoc } from '../fragments/GiftVoucherCodeData';
import { FinderApplicationSpecsFragmentDoc } from '../fragments/FinderApplicationSpecs';
import { ApplicationFinderVehicleSpecsFragmentDoc } from '../fragments/ApplicationFinderVehicleSpecs';
import { FinderApplicationPublicModuleDebugJourneyFragmentDoc } from '../fragments/FinderApplicationPublicModuleDebugJourney';
import { FinderApplicationPrivateModuleDebugJourneyFragmentDoc } from '../fragments/FinderApplicationPrivateModuleDebugJourney';
import { CtsModuleSettingDataFragmentDoc } from '../fragments/CtsModuleSettingData';
import { DealerPriceDisclaimerDataFragmentDoc } from '../fragments/DealerPriceDisclaimerData';
import { FinderApplicationPublicModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPublicModuleSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import { ModuleDisclaimersDataFragmentDoc } from '../fragments/ModuleDisclaimersData';
import { FinderApplicationPrivateModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPrivateModuleSpecs';
import { FlexibleDiscountDataFragmentDoc } from '../fragments/FlexibleDiscountData';
import { LaunchpadApplicationSpecFragmentDoc } from '../fragments/LaunchpadApplicationSpec';
import { LaunchpadModuleDebugJourneyFragmentDoc } from '../fragments/LaunchpadModuleDebugJourney';
import { SalesOfferApplicationSpecsFragmentDoc } from '../fragments/SalesOfferApplicationSpecs';
import { SalesOfferModuleDebugJourneyFragmentDoc } from '../fragments/SalesOfferModuleDebugJourney';
import { ApplicationAdyenDepositDataFragmentDoc } from '../fragments/ApplicationAdyenDepositData';
import { ApplicationPorscheDepositDataFragmentDoc } from '../fragments/ApplicationPorscheDepositData';
import { ApplicationFiservDepositDataFragmentDoc } from '../fragments/ApplicationFiservDepositData';
import { ApplicationPayGateDepositDataFragmentDoc } from '../fragments/ApplicationPayGateDepositData';
import { ApplicationTtbDepositDataFragmentDoc } from '../fragments/ApplicationTtbDepositData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type UpdateFinderApplicationMutationVariables = SchemaTypes.Exact<{
  token: SchemaTypes.Scalars['String']['input'];
  insurancing?: SchemaTypes.InputMaybe<SchemaTypes.ApplicationInsuranceSettings>;
  financing?: SchemaTypes.InputMaybe<SchemaTypes.ApplicationFinanceSettings>;
  configuration: SchemaTypes.FinderApplicationConfigurationPayload;
  tradeInVehicle: Array<SchemaTypes.TradeInVehiclePayload> | SchemaTypes.TradeInVehiclePayload;
  promoCodeId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  languageId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type UpdateFinderApplicationMutation = (
  { __typename: 'Mutation' }
  & { result: (
    { __typename: 'ApplicationJourney' }
    & DebugJourneyDataFragment
  ) }
);


export const UpdateFinderApplicationDocument = /*#__PURE__*/ gql`
    mutation updateFinderApplication($token: String!, $insurancing: ApplicationInsuranceSettings, $financing: ApplicationFinanceSettings, $configuration: FinderApplicationConfigurationPayload!, $tradeInVehicle: [TradeInVehiclePayload!]!, $promoCodeId: ObjectID, $languageId: ObjectID) {
  result: updateFinderApplication(
    token: $token
    insurancing: $insurancing
    financing: $financing
    configuration: $configuration
    tradeInVehicle: $tradeInVehicle
    promoCodeId: $promoCodeId
    languageId: $languageId
  ) {
    ...DebugJourneyData
  }
}
    ${DebugJourneyDataFragmentDoc}
${ApplicationStageDataFragmentDoc}
${StandardApplicationSpecFragmentDoc}
${DealerJourneyDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${StandardApplicationModuleDebugJourneyFragmentDoc}
${AppointmentModuleApplicationJourneyFragmentDoc}
${AppointmentTimeSlotDataFragmentDoc}
${NamirialSigningModuleSpecsFragmentDoc}
${NamirialSettingsSpecFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${VisitAppointmentModuleApplicationJourneyFragmentDoc}
${TimeSlotDataFragmentDoc}
${KycExtraSettingsSpecsFragmentDoc}
${TradeInVehicleDataFragmentDoc}
${ApplicationAgreementDataFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${MarketingPlatformSpecsFragmentDoc}
${MarketingPlatformsAgreedSpecsFragmentDoc}
${UsersOptionsDataFragmentDoc}
${KycFieldSpecsFragmentDoc}
${ApplicationDocumentDataFragmentDoc}
${CustomerSpecsFragmentDoc}
${LocalCustomerDataFragmentDoc}
${LocalCustomerFieldDataFragmentDoc}
${CorporateCustomerDataFragmentDoc}
${GuarantorDataFragmentDoc}
${JourneyDraftFlowFragmentDoc}
${ApplicationJourneyDepositFragmentDoc}
${ApplicationFinancingDataFragmentDoc}
${ApplicationInsurancingDataFragmentDoc}
${NamirialSigningDataFragmentDoc}
${ApplicationVariantSpecFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${ApplicationModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${ApplicationQuotationDataFragmentDoc}
${ApplicationQuotationOptionDataFragmentDoc}
${LeadDataFragmentDoc}
${StandardLeadDataFragmentDoc}
${VehicleSpecsFragmentDoc}
${LocalVariantBaseSpecsFragmentDoc}
${LocalModelSpecsFragmentDoc}
${FinderVehicleSpecsFragmentDoc}
${FullListingValueFragmentDoc}
${FormattedDateDataFragmentDoc}
${LocalizedStringDataFragmentDoc}
${LocalizedValueDataFragmentDoc}
${NumberUnitDataFragmentDoc}
${FinderLeadDataFragmentDoc}
${EventLeadDataFragmentDoc}
${ApplicationEventCustomizedFieldDataFragmentDoc}
${LaunchpadLeadDataFragmentDoc}
${ConfiguratorLeadDataFragmentDoc}
${ConfiguratorJourneyBlocksDataFragmentDoc}
${BlockDetailsFragmentDoc}
${OptionSettingDetailsFragmentDoc}
${MobilityLeadDataFragmentDoc}
${LaunchpadModuleSpecsForApplicationFragmentDoc}
${DealerVehiclesSpecsFragmentDoc}
${DealerApplicationFragmentFragmentDoc}
${DealerContactFragmentFragmentDoc}
${DealerSocialMediaFragmentFragmentDoc}
${ReferenceApplicationDataFragmentDoc}
${ReferenceDepositDataFragmentDoc}
${ReferenceFinancingDataFragmentDoc}
${ReferenceInsuranceDataFragmentDoc}
${SalesOfferSpecsFragmentDoc}
${VehicleSalesOfferSpecsFragmentDoc}
${LocalVariantSpecsFragmentDoc}
${PorscheVehicleDataSpecsFragmentDoc}
${PorscheVehicleDataFeatureSpecsFragmentDoc}
${PorscheVehicleImagesSpecsFragmentDoc}
${LocalFittedOptionsSpecsFragmentDoc}
${SalesOfferDocumentDataFragmentDoc}
${MainDetailsSalesOfferSpecsFragmentDoc}
${TradeInSalesOfferSpecsFragmentDoc}
${FinanceSalesOfferSpecsFragmentDoc}
${InsuranceSalesOfferSpecsFragmentDoc}
${DepositSalesOfferSpecsFragmentDoc}
${VsaSalesOfferSpecsFragmentDoc}
${SalesOfferModuleSpecsFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${DealershipSettingSpecDataFragmentDoc}
${ApplicationMarketTypeFragmentFragmentDoc}
${DealerMarketDataFragmentDoc}
${BankDealerMarketDataFragmentDoc}
${NzFeesDealerMarketDataFragmentDoc}
${DealerDisclaimersConfiguratorDataFragmentDoc}
${BankDetailsDataFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${BankIntegrationDataFragmentDoc}
${UploadFileFormDataFragmentDoc}
${FinanceProductDetailsDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${PeriodDataFragmentDoc}
${VehicleReferenceParametersDataFragmentDoc}
${PaymentSettingsDetailsFragmentDoc}
${LoanSettingsDetailsFragmentDoc}
${TermSettingsDetailsFragmentDoc}
${InterestRateSettingsDetailsFragmentDoc}
${DownPaymentSettingsDetailsFragmentDoc}
${BalloonSettingsDetailsFragmentDoc}
${BalloonGfvSettingsDetailsFragmentDoc}
${LeaseSettingsDetailsFragmentDoc}
${DepositSettingsDetailsFragmentDoc}
${ResidualValueSettingsDetailsFragmentDoc}
${LocalUcclLeasingOnlyDetailsFragmentDoc}
${CounterSettingsSpecsFragmentDoc}
${DealerFinanceProductsSpecsFragmentDoc}
${FinanceProductListDataFragmentDoc}
${DealerInsuranceProductsSpecsFragmentDoc}
${InsuranceProductListDataFragmentDoc}
${ErgoLookupTableSettingsDetailsFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${SalesOfferModuleEmailContentsSpecsFragmentDoc}
${SalesOfferEmailContentsSpecsFragmentDoc}
${SalesOfferKycPresetSpecsFragmentDoc}
${SalesOfferConsentsSpecsFragmentDoc}
${SalesOfferSigningsSpecsFragmentDoc}
${EventApplicationSpecFragmentDoc}
${JourneyEventDataFragmentDoc}
${EventModuleDataFragmentDoc}
${LocalCustomerManagementModuleKycFieldSpecsFragmentDoc}
${AppointmentModuleOnEventModuleDataFragmentDoc}
${AppointmentModuleEmailContentsSpecsFragmentDoc}
${AppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${AppointmentModuleEmailContentSpecsFragmentDoc}
${DealerBooleanSettingDataFragmentDoc}
${AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc}
${EventApplicationModuleEmailContentSpecsFragmentDoc}
${TranslatedTextDataFragmentDoc}
${DepositAmountDataFragmentDoc}
${KycPresetsSpecFragmentDoc}
${CustomizedFieldDataFragmentDoc}
${EventEmailContentSpecsFragmentDoc}
${ThankYouPageContentSpecsFragmentDoc}
${CustomTestDriveBookingSlotsDataFragmentDoc}
${TestDriveFixedPeriodDataFragmentDoc}
${TestDriveBookingWindowSettingsDataFragmentDoc}
${EventApplicationModuleDebugJourneyFragmentDoc}
${ConfiguratorApplicationSpecFragmentDoc}
${LocalVariantPublicSpecsFragmentDoc}
${LocalModelPublicSpecsFragmentDoc}
${LocalMakePublicSpecsFragmentDoc}
${VariantConfiguratorJourneyDataFragmentDoc}
${MatrixDataFragmentDoc}
${InventoryDetailsPublicDataFragmentDoc}
${StockInventorySpecsFragmentDoc}
${CompanyPublicSpecsFragmentDoc}
${StockBlockingPeriodDataFragmentDoc}
${ConfiguratorInventoryPublicSpecsFragmentDoc}
${MobilityInventoryPublicSpecsFragmentDoc}
${MobilityModuleSpecsFragmentDoc}
${DealerBookingCodeSpecsFragmentDoc}
${MobilitySigningSettingSpecsFragmentDoc}
${KycPresetsOptionsDataFragmentDoc}
${MobilityModuleEmailScenarioContentSpecsFragmentDoc}
${MobilityCustomerEmailContentDataFragmentDoc}
${MobilityEmailContentDataFragmentDoc}
${MobilityOperatorEmailContentDataFragmentDoc}
${MobilityHomeDeliveryDataFragmentDoc}
${ConfiguratorApplicationModuleDebugJourneyFragmentDoc}
${PromoCodeSpecFragmentDoc}
${GiftPromoTypeDataFragmentDoc}
${DiscountPromoTypeDataFragmentDoc}
${MobilityApplicationSpecsFragmentDoc}
${MobilitySnapshotDataFragmentDoc}
${MobilityBookingLocationHomeDataFragmentDoc}
${MobilityBookingLocationPickupDataFragmentDoc}
${PromoCodeDataForApplicationFragmentDoc}
${GiftVoucherCodeDataFragmentDoc}
${FinderApplicationSpecsFragmentDoc}
${ApplicationFinderVehicleSpecsFragmentDoc}
${FinderApplicationPublicModuleDebugJourneyFragmentDoc}
${FinderApplicationPrivateModuleDebugJourneyFragmentDoc}
${CtsModuleSettingDataFragmentDoc}
${DealerPriceDisclaimerDataFragmentDoc}
${FinderApplicationPublicModuleSpecsFragmentDoc}
${FinderApplicationModuleEmailContentSpecsFragmentDoc}
${ModuleDisclaimersDataFragmentDoc}
${FinderApplicationPrivateModuleSpecsFragmentDoc}
${FlexibleDiscountDataFragmentDoc}
${LaunchpadApplicationSpecFragmentDoc}
${LaunchpadModuleDebugJourneyFragmentDoc}
${SalesOfferApplicationSpecsFragmentDoc}
${SalesOfferModuleDebugJourneyFragmentDoc}
${ApplicationAdyenDepositDataFragmentDoc}
${ApplicationPorscheDepositDataFragmentDoc}
${ApplicationFiservDepositDataFragmentDoc}
${ApplicationPayGateDepositDataFragmentDoc}
${ApplicationTtbDepositDataFragmentDoc}`;
export type UpdateFinderApplicationMutationFn = Apollo.MutationFunction<UpdateFinderApplicationMutation, UpdateFinderApplicationMutationVariables>;

/**
 * __useUpdateFinderApplicationMutation__
 *
 * To run a mutation, you first call `useUpdateFinderApplicationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateFinderApplicationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateFinderApplicationMutation, { data, loading, error }] = useUpdateFinderApplicationMutation({
 *   variables: {
 *      token: // value for 'token'
 *      insurancing: // value for 'insurancing'
 *      financing: // value for 'financing'
 *      configuration: // value for 'configuration'
 *      tradeInVehicle: // value for 'tradeInVehicle'
 *      promoCodeId: // value for 'promoCodeId'
 *      languageId: // value for 'languageId'
 *   },
 * });
 */
export function useUpdateFinderApplicationMutation(baseOptions?: Apollo.MutationHookOptions<UpdateFinderApplicationMutation, UpdateFinderApplicationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateFinderApplicationMutation, UpdateFinderApplicationMutationVariables>(UpdateFinderApplicationDocument, options);
      }
export type UpdateFinderApplicationMutationHookResult = ReturnType<typeof useUpdateFinderApplicationMutation>;
export type UpdateFinderApplicationMutationResult = Apollo.MutationResult<UpdateFinderApplicationMutation>;
export type UpdateFinderApplicationMutationOptions = Apollo.BaseMutationOptions<UpdateFinderApplicationMutation, UpdateFinderApplicationMutationVariables>;