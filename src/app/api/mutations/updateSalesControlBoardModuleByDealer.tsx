import type * as SchemaTypes from '../types';

import type { ModuleInDealerSpecs_AdyenPaymentModule_Fragment, ModuleInDealerSpecs_AppointmentModule_Fragment, ModuleInDealerSpecs_AutoplayModule_Fragment, ModuleInDealerSpecs_BankModule_Fragment, ModuleInDealerSpecs_BasicSigningModule_Fragment, ModuleInDealerSpecs_CapModule_Fragment, ModuleInDealerSpecs_ConfiguratorModule_Fragment, ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleInDealerSpecs_CtsModule_Fragment, ModuleInDealerSpecs_DocusignModule_Fragment, ModuleInDealerSpecs_EventApplicationModule_Fragment, ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment, ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment, ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment, ModuleInDealerSpecs_FiservPaymentModule_Fragment, ModuleInDealerSpecs_GiftVoucherModule_Fragment, ModuleInDealerSpecs_InsuranceModule_Fragment, ModuleInDealerSpecs_LabelsModule_Fragment, ModuleInDealerSpecs_LaunchPadModule_Fragment, ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment, ModuleInDealerSpecs_MaintenanceModule_Fragment, ModuleInDealerSpecs_MarketingModule_Fragment, ModuleInDealerSpecs_MobilityModule_Fragment, ModuleInDealerSpecs_MyInfoModule_Fragment, ModuleInDealerSpecs_NamirialSigningModule_Fragment, ModuleInDealerSpecs_OfrModule_Fragment, ModuleInDealerSpecs_OidcModule_Fragment, ModuleInDealerSpecs_PayGatePaymentModule_Fragment, ModuleInDealerSpecs_PorscheIdModule_Fragment, ModuleInDealerSpecs_PorscheMasterDataModule_Fragment, ModuleInDealerSpecs_PorschePaymentModule_Fragment, ModuleInDealerSpecs_PorscheRetainModule_Fragment, ModuleInDealerSpecs_PromoCodeModule_Fragment, ModuleInDealerSpecs_SalesControlBoardModule_Fragment, ModuleInDealerSpecs_SalesOfferModule_Fragment, ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment, ModuleInDealerSpecs_StandardApplicationModule_Fragment, ModuleInDealerSpecs_TradeInModule_Fragment, ModuleInDealerSpecs_TtbPaymentModule_Fragment, ModuleInDealerSpecs_UserlikeChatbotModule_Fragment, ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleInDealerSpecs_VisitAppointmentModule_Fragment, ModuleInDealerSpecs_WebsiteModule_Fragment, ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment } from '../fragments/ModuleInDealerSpecs';
import type { StandardApplicationModuleInDealerSpecsFragment } from '../fragments/StandardApplicationModuleInDealerSpecs';
import type { DealerPriceDisclaimerDataFragment } from '../fragments/DealerPriceDisclaimerData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from '../fragments/ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from '../fragments/DealerMarketData';
import type { BankDealerMarketDataFragment } from '../fragments/BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from '../fragments/NzFeesDealerMarketData';
import type { DealerVehiclesSpecsFragment } from '../fragments/DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from '../fragments/DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductListData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OfrModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from '../fragments/BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from '../fragments/BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from '../fragments/DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from '../fragments/InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from '../fragments/ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { DealerUploadedFileWithPreviewDataFragment } from '../fragments/DealerUploadedFileWithPreview';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from '../fragments/DealerBooleanSettingData';
import type { EventApplicationModuleInDealerSpecsFragment } from '../fragments/EventApplicationModuleInDealerSpecs';
import type { EventApplicationModuleEmailContentSpecsFragment, EventEmailContentSpecsFragment } from '../fragments/EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { ConfiguratorModuleInDealerSpecsFragment } from '../fragments/ConfiguratorModuleInDealerSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from '../fragments/DealershipSettingSpecData';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { ConfiguratorModuleEmailContentSpecsFragment } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import type { MobilityModuleInDealerSpecsFragment } from '../fragments/MobilityModuleInDealerSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from '../fragments/MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from '../fragments/MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from '../fragments/MobilityOperatorEmailContentData';
import type { DealerBookingCodeSpecsFragment } from '../fragments/DealerBookingCodeSpecs';
import type { MobilityHomeDeliveryDataFragment } from '../fragments/MobilityHomeDeliveryData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { FinderApplicationPublicModuleInDealerSpecsFragment } from '../fragments/FinderApplicationPublicModuleInDealerSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import type { FinderApplicationPrivateModuleInDealerSpecsFragment } from '../fragments/FinderApplicationPrivateModuleInDealerSpecs';
import type { AppointmentModuleInDealerSpecsFragment } from '../fragments/AppointmentModuleInDealerSpecs';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from '../fragments/AppointmentModuleEmailContentsSpecs';
import type { VisitAppointmentModuleInDealerSpecsFragment } from '../fragments/VisitAppointmentModuleInDealerSpecs';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import type { GiftVoucherModuleInDealerSpecsFragment } from '../fragments/GiftVoucherModuleInDealerSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import type { LaunchPadModuleInDealerSpecsFragment } from '../fragments/LaunchPadModuleInDealerSpecs';
import type { SalesOfferModuleInDealerSpecsFragment } from '../fragments/SalesOfferModuleInDealerSpecs';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import type { SalesControlBoardModuleInDealerSpecsFragment } from '../fragments/SalesControlBoardModuleInDealerSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from '../fragments/DealerIntData';
import type { OfrModuleInDealerSpecsFragment } from '../fragments/OFRModuleInDealerSpecs';
import type { OfrModuleEmailContentsSpecsFragment, OfrSalesConsultantEmailContentSpecsFragment, OfrSalesConsultantEmailContentContextSpecsFragment, OfrCustomerEmailContentSpecsFragment, OfrCustomerEmailContentContextSpecsFragment } from '../fragments/OFRModuleEmailContentsSpecs';
import type { OfrEquityDataFragment } from '../fragments/OFREquityData';
import { gql } from '@apollo/client';
import { ModuleInDealerSpecsFragmentDoc } from '../fragments/ModuleInDealerSpecs';
import { StandardApplicationModuleInDealerSpecsFragmentDoc } from '../fragments/StandardApplicationModuleInDealerSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from '../fragments/DealerPriceDisclaimerData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { ApplicationMarketTypeFragmentFragmentDoc } from '../fragments/ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from '../fragments/DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from '../fragments/BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from '../fragments/NzFeesDealerMarketData';
import { DealerVehiclesSpecsFragmentDoc } from '../fragments/DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from '../fragments/DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from '../fragments/FinanceProductListData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from '../fragments/BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from '../fragments/BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from '../fragments/DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from '../fragments/InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from '../fragments/ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from '../fragments/DealerUploadedFileWithPreview';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from '../fragments/DealerBooleanSettingData';
import { EventApplicationModuleInDealerSpecsFragmentDoc } from '../fragments/EventApplicationModuleInDealerSpecs';
import { EventApplicationModuleEmailContentSpecsFragmentDoc, EventEmailContentSpecsFragmentDoc } from '../fragments/EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { ConfiguratorModuleInDealerSpecsFragmentDoc } from '../fragments/ConfiguratorModuleInDealerSpecs';
import { DealershipSettingSpecDataFragmentDoc } from '../fragments/DealershipSettingSpecData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import { MobilityModuleInDealerSpecsFragmentDoc } from '../fragments/MobilityModuleInDealerSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from '../fragments/MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from '../fragments/MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from '../fragments/MobilityOperatorEmailContentData';
import { DealerBookingCodeSpecsFragmentDoc } from '../fragments/DealerBookingCodeSpecs';
import { MobilityHomeDeliveryDataFragmentDoc } from '../fragments/MobilityHomeDeliveryData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { FinderApplicationPublicModuleInDealerSpecsFragmentDoc } from '../fragments/FinderApplicationPublicModuleInDealerSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import { FinderApplicationPrivateModuleInDealerSpecsFragmentDoc } from '../fragments/FinderApplicationPrivateModuleInDealerSpecs';
import { AppointmentModuleInDealerSpecsFragmentDoc } from '../fragments/AppointmentModuleInDealerSpecs';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from '../fragments/AppointmentModuleEmailContentsSpecs';
import { VisitAppointmentModuleInDealerSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleInDealerSpecs';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import { GiftVoucherModuleInDealerSpecsFragmentDoc } from '../fragments/GiftVoucherModuleInDealerSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import { LaunchPadModuleInDealerSpecsFragmentDoc } from '../fragments/LaunchPadModuleInDealerSpecs';
import { SalesOfferModuleInDealerSpecsFragmentDoc } from '../fragments/SalesOfferModuleInDealerSpecs';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import { SalesControlBoardModuleInDealerSpecsFragmentDoc } from '../fragments/SalesControlBoardModuleInDealerSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from '../fragments/DealerIntData';
import { OfrModuleInDealerSpecsFragmentDoc } from '../fragments/OFRModuleInDealerSpecs';
import { OfrModuleEmailContentsSpecsFragmentDoc, OfrSalesConsultantEmailContentSpecsFragmentDoc, OfrSalesConsultantEmailContentContextSpecsFragmentDoc, OfrCustomerEmailContentSpecsFragmentDoc, OfrCustomerEmailContentContextSpecsFragmentDoc } from '../fragments/OFRModuleEmailContentsSpecs';
import { OfrEquityDataFragmentDoc } from '../fragments/OFREquityData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type UpdateSalesControlBoardModuleByDealerMutationVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
  dealerId: SchemaTypes.Scalars['ObjectID']['input'];
  retailsMonthlyTarget: SchemaTypes.Scalars['Int']['input'];
  testDriveMonthlyTarget: SchemaTypes.Scalars['Int']['input'];
  orderIntakesMonthlyTarget: SchemaTypes.Scalars['Int']['input'];
  financeCommissionMonthlyTarget: SchemaTypes.Scalars['Float']['input'];
  insuranceCommissionMonthlyTarget: SchemaTypes.Scalars['Float']['input'];
  salesConsultantsAssignments: Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type UpdateSalesControlBoardModuleByDealerMutation = (
  { __typename: 'Mutation' }
  & { module?: SchemaTypes.Maybe<(
    { __typename: 'AdyenPaymentModule' }
    & ModuleInDealerSpecs_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & ModuleInDealerSpecs_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & ModuleInDealerSpecs_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & ModuleInDealerSpecs_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & ModuleInDealerSpecs_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & ModuleInDealerSpecs_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & ModuleInDealerSpecs_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & ModuleInDealerSpecs_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & ModuleInDealerSpecs_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & ModuleInDealerSpecs_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & ModuleInDealerSpecs_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & ModuleInDealerSpecs_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & ModuleInDealerSpecs_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & ModuleInDealerSpecs_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & ModuleInDealerSpecs_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & ModuleInDealerSpecs_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & ModuleInDealerSpecs_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & ModuleInDealerSpecs_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & ModuleInDealerSpecs_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & ModuleInDealerSpecs_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OFRModule' }
    & ModuleInDealerSpecs_OfrModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & ModuleInDealerSpecs_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & ModuleInDealerSpecs_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & ModuleInDealerSpecs_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & ModuleInDealerSpecs_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & ModuleInDealerSpecs_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & ModuleInDealerSpecs_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & ModuleInDealerSpecs_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & ModuleInDealerSpecs_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & ModuleInDealerSpecs_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & ModuleInDealerSpecs_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & ModuleInDealerSpecs_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & ModuleInDealerSpecs_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & ModuleInDealerSpecs_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & ModuleInDealerSpecs_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & ModuleInDealerSpecs_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment
  )> }
);


export const UpdateSalesControlBoardModuleByDealerDocument = /*#__PURE__*/ gql`
    mutation updateSalesControlBoardModuleByDealer($moduleId: ObjectID!, $dealerId: ObjectID!, $retailsMonthlyTarget: Int!, $testDriveMonthlyTarget: Int!, $orderIntakesMonthlyTarget: Int!, $financeCommissionMonthlyTarget: Float!, $insuranceCommissionMonthlyTarget: Float!, $salesConsultantsAssignments: [ObjectID!]!) {
  module: updateSalesControlBoardModuleByDealer(
    moduleId: $moduleId
    dealerId: $dealerId
    retailsMonthlyTarget: $retailsMonthlyTarget
    testDriveMonthlyTarget: $testDriveMonthlyTarget
    orderIntakesMonthlyTarget: $orderIntakesMonthlyTarget
    financeCommissionMonthlyTarget: $financeCommissionMonthlyTarget
    insuranceCommissionMonthlyTarget: $insuranceCommissionMonthlyTarget
    salesConsultantsAssignments: $salesConsultantsAssignments
  ) {
    ...ModuleInDealerSpecs
  }
}
    ${ModuleInDealerSpecsFragmentDoc}
${StandardApplicationModuleInDealerSpecsFragmentDoc}
${DealerPriceDisclaimerDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${ApplicationMarketTypeFragmentFragmentDoc}
${DealerMarketDataFragmentDoc}
${BankDealerMarketDataFragmentDoc}
${NzFeesDealerMarketDataFragmentDoc}
${DealerVehiclesSpecsFragmentDoc}
${DealerFinanceProductsSpecsFragmentDoc}
${FinanceProductListDataFragmentDoc}
${PeriodDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${VehicleReferenceParametersDataFragmentDoc}
${BalloonSettingsDetailsFragmentDoc}
${BalloonGfvSettingsDetailsFragmentDoc}
${DealerInsuranceProductsSpecsFragmentDoc}
${InsuranceProductListDataFragmentDoc}
${ErgoLookupTableSettingsDetailsFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${StandardApplicationModuleEmailContentsSpecsFragmentDoc}
${StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc}
${StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${DealerUploadedFileWithPreviewDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${DealerBooleanSettingDataFragmentDoc}
${StandardApplicationModuleEmailContentSpecsFragmentDoc}
${StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc}
${EventApplicationModuleInDealerSpecsFragmentDoc}
${EventApplicationModuleEmailContentSpecsFragmentDoc}
${TranslatedTextDataFragmentDoc}
${ConfiguratorModuleInDealerSpecsFragmentDoc}
${DealershipSettingSpecDataFragmentDoc}
${DealerDisclaimersConfiguratorDataFragmentDoc}
${ConfiguratorModuleEmailContentSpecsFragmentDoc}
${MobilityModuleInDealerSpecsFragmentDoc}
${MobilityModuleEmailScenarioContentSpecsFragmentDoc}
${MobilityCustomerEmailContentDataFragmentDoc}
${MobilityEmailContentDataFragmentDoc}
${MobilityOperatorEmailContentDataFragmentDoc}
${DealerBookingCodeSpecsFragmentDoc}
${MobilityHomeDeliveryDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${MobilityLocationDataFragmentDoc}
${FinderApplicationPublicModuleInDealerSpecsFragmentDoc}
${FinderApplicationModuleEmailContentSpecsFragmentDoc}
${FinderApplicationPrivateModuleInDealerSpecsFragmentDoc}
${AppointmentModuleInDealerSpecsFragmentDoc}
${AppointmentModuleEmailContentsSpecsFragmentDoc}
${AppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${AppointmentModuleEmailContentSpecsFragmentDoc}
${AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc}
${VisitAppointmentModuleInDealerSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentsSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${GiftVoucherModuleInDealerSpecsFragmentDoc}
${GiftVoucherModuleEmailContentsSpecsFragmentDoc}
${GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc}
${GiftVoucherModuleEmailDataFragmentDoc}
${LaunchPadModuleInDealerSpecsFragmentDoc}
${SalesOfferModuleInDealerSpecsFragmentDoc}
${SalesOfferModuleEmailContentsSpecsFragmentDoc}
${SalesOfferEmailContentsSpecsFragmentDoc}
${SalesControlBoardModuleInDealerSpecsFragmentDoc}
${DealerIntDataFragmentDoc}
${DealerFloatDataFragmentDoc}
${DealerObjectIdDataFragmentDoc}
${OfrModuleInDealerSpecsFragmentDoc}
${OfrModuleEmailContentsSpecsFragmentDoc}
${OfrSalesConsultantEmailContentSpecsFragmentDoc}
${OfrSalesConsultantEmailContentContextSpecsFragmentDoc}
${OfrCustomerEmailContentSpecsFragmentDoc}
${OfrCustomerEmailContentContextSpecsFragmentDoc}
${OfrEquityDataFragmentDoc}`;
export type UpdateSalesControlBoardModuleByDealerMutationFn = Apollo.MutationFunction<UpdateSalesControlBoardModuleByDealerMutation, UpdateSalesControlBoardModuleByDealerMutationVariables>;

/**
 * __useUpdateSalesControlBoardModuleByDealerMutation__
 *
 * To run a mutation, you first call `useUpdateSalesControlBoardModuleByDealerMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateSalesControlBoardModuleByDealerMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateSalesControlBoardModuleByDealerMutation, { data, loading, error }] = useUpdateSalesControlBoardModuleByDealerMutation({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      dealerId: // value for 'dealerId'
 *      retailsMonthlyTarget: // value for 'retailsMonthlyTarget'
 *      testDriveMonthlyTarget: // value for 'testDriveMonthlyTarget'
 *      orderIntakesMonthlyTarget: // value for 'orderIntakesMonthlyTarget'
 *      financeCommissionMonthlyTarget: // value for 'financeCommissionMonthlyTarget'
 *      insuranceCommissionMonthlyTarget: // value for 'insuranceCommissionMonthlyTarget'
 *      salesConsultantsAssignments: // value for 'salesConsultantsAssignments'
 *   },
 * });
 */
export function useUpdateSalesControlBoardModuleByDealerMutation(baseOptions?: Apollo.MutationHookOptions<UpdateSalesControlBoardModuleByDealerMutation, UpdateSalesControlBoardModuleByDealerMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateSalesControlBoardModuleByDealerMutation, UpdateSalesControlBoardModuleByDealerMutationVariables>(UpdateSalesControlBoardModuleByDealerDocument, options);
      }
export type UpdateSalesControlBoardModuleByDealerMutationHookResult = ReturnType<typeof useUpdateSalesControlBoardModuleByDealerMutation>;
export type UpdateSalesControlBoardModuleByDealerMutationResult = Apollo.MutationResult<UpdateSalesControlBoardModuleByDealerMutation>;
export type UpdateSalesControlBoardModuleByDealerMutationOptions = Apollo.BaseMutationOptions<UpdateSalesControlBoardModuleByDealerMutation, UpdateSalesControlBoardModuleByDealerMutationVariables>;