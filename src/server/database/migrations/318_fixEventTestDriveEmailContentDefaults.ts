import type { AnyBulkWriteOperation } from 'mongodb';
import type { Event, EventApplicationModule, AppointmentModule } from '../documents';
import { ModuleType } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

// Default translation values that would have been set by getTestDriveCustomerEmailInitialContents
// These are the actual translated strings from emails.json that need to be replaced
const incorrectDefaults = {
    submitConfirmation: {
        subject: '{{companyName}}: Test Drive Submitted',
        introTitle: 'You will be contacted shortly to confirm the Test Drive',
        contentText:
            'Please note Test Drive is subject to availability of the model and the vehicle. ' +
            'Booking will be cancelled if Vehicle is purchased.',
    },
    bookingConfirmation: {
        subject: '{{companyName}}: Test Drive Confirmed',
        introTitle: 'Your Test Drive is confirmed',
        contentText:
            'Please note Test Drive is subject to availability of the vehicle. ' +
            'Booking will be cancelled if Vehicle is purchased.',
    },
    bookingAmendment: {
        subject: '{{companyName}}: Test Drive Booking Updated',
        introTitle: 'Your updated Test Drive Booking',
        contentText:
            'A change in Test Drive Booking details has been made. Please note Test Drive is subject to ' +
            'availability of the vehicle. Booking will be cancelled if Vehicle is purchased.' +
            '<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**',
    },
    bookingCancellation: {
        subject: '{{companyName}}: Test Drive {{identifier}} Cancelled',
        introTitle: 'Your Test Drive has been cancelled',
        contentText: 'Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}.',
    },
    endTestDriveWithProcess: {
        subject: '{{companyName}}: Test Drive Ended',
        introTitle: 'Enjoyed your Test Drive car and like to own it?',
        contentText: 'Reserve the car via this link: <url>{{link}}</url>',
    },
    completeTestDriveWithoutProcess: {
        subject: '{{companyName}}: Test Drive Completed',
        introTitle: 'Enjoyed your Test Drive car and like to own it?',
        contentText: 'Reserve the car via this link: <url>{{link}}</url>',
    },
};

export default {
    identifier: '318_fixEventTestDriveEmailContentDefaults',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        // Build the query to find events with default translation values
        const matchConditions = [];

        // Check each customer email type for default values
        Object.keys(incorrectDefaults).forEach(emailType => {
            const defaults = incorrectDefaults[emailType as keyof typeof incorrectDefaults];

            matchConditions.push({
                [`emailContents.testDrive.customer.${emailType}.subject.defaultValue.defaultValue`]: defaults.subject,
                [`emailContents.testDrive.customer.${emailType}.introTitle.defaultValue.defaultValue`]:
                    defaults.introTitle,
                [`emailContents.testDrive.customer.${emailType}.contentText.defaultValue.defaultValue`]:
                    defaults.contentText,
                [`emailContents.testDrive.customer.${emailType}.isSummaryVehicleVisible.defaultValue`]: true,
            });
        });

        const problematicEvents = await db
            .collection<Event>('events')
            .find({
                $and: matchConditions,
                'emailContents.testDrive.customer': { $exists: true },
            })
            .toArray();

        if (problematicEvents.length === 0) {
            console.warn('No events need to be updated. Migration completed.');

            return;
        }

        // Get unique module IDs from problematic events
        const uniqueModuleIds = Array.from(new Set(problematicEvents.map(event => event.moduleId)));

        // Fetch all EventApplicationModules in one batch
        const eventModules = await db
            .collection<EventApplicationModule>('modules')
            .find({
                _id: { $in: uniqueModuleIds },
                _type: ModuleType.EventApplicationModule,
            })
            .toArray();

        // Create a map for quick module lookup
        const eventModuleMap = new Map(eventModules.map(module => [module._id.toString(), module]));

        // Get unique appointment module IDs
        const appointmentModuleIds = eventModules
            .filter(module => module.appointmentModuleId)
            .map(module => module.appointmentModuleId!)
            .filter(Boolean);

        // Fetch all AppointmentModules in one batch
        const appointmentModules = await db
            .collection<AppointmentModule>('modules')
            .find({
                _id: { $in: appointmentModuleIds },
                _type: ModuleType.AppointmentModule,
            })
            .toArray();

        // Create a map for quick appointment module lookup
        const appointmentModuleMap = new Map(appointmentModules.map(module => [module._id.toString(), module]));

        // Prepare bulk write operations
        const bulkWriteOps: AnyBulkWriteOperation[] = [];

        for (const event of problematicEvents) {
            const eventModule = eventModuleMap.get(event.moduleId.toString());

            if (!eventModule) {
                console.warn(`  - Warning: EventApplicationModule not found for event ${event._id}, skipping`);
                continue;
            }

            if (!eventModule.appointmentModuleId) {
                console.warn(`  - Warning: Event ${event._id} has no appointmentModuleId, skipping`);
                continue;
            }

            const appointmentModule = appointmentModuleMap.get(eventModule.appointmentModuleId.toString());

            if (!appointmentModule) {
                console.warn(`  - Warning: AppointmentModule not found for event ${event._id}, skipping`);
                continue;
            }

            if (!appointmentModule.emailContents?.customer) {
                console.warn(
                    `  - Warning: AppointmentModule ${appointmentModule._id} has no customer email contents, skipping`
                );
                continue;
            }

            const appointmentEmailContents = appointmentModule.emailContents.customer;

            // Check if this event actually has the problematic default values
            let hasProblematicValues = false;
            const updateFields: Record<string, any> = {};

            Object.keys(incorrectDefaults).forEach(emailType => {
                const defaults = incorrectDefaults[emailType as keyof typeof incorrectDefaults];
                const eventEmailContent =
                    event.emailContents?.testDrive?.customer?.[
                        emailType as keyof typeof event.emailContents.testDrive.customer
                    ];

                if (eventEmailContent) {
                    const hasDefaultSubject =
                        eventEmailContent.subject?.defaultValue?.defaultValue === defaults.subject;
                    const hasDefaultIntroTitle =
                        eventEmailContent.introTitle?.defaultValue?.defaultValue === defaults.introTitle;
                    const hasDefaultContentText =
                        eventEmailContent.contentText?.defaultValue?.defaultValue === defaults.contentText;

                    if (hasDefaultSubject || hasDefaultIntroTitle || hasDefaultContentText) {
                        hasProblematicValues = true;

                        // Replace with values from appointment module
                        const appointmentEmailContent =
                            appointmentEmailContents[emailType as keyof typeof appointmentEmailContents];
                        if (appointmentEmailContent) {
                            updateFields[`emailContents.testDrive.customer.${emailType}`] = appointmentEmailContent;
                        }
                    }
                }
            });

            if (hasProblematicValues && Object.keys(updateFields).length > 0) {
                bulkWriteOps.push({
                    updateOne: {
                        filter: { _id: event._id },
                        update: { $set: updateFields },
                    },
                });
            }
        }

        if (bulkWriteOps.length > 0) {
            await db.collection('events').bulkWrite(bulkWriteOps);
        }
    },
};
